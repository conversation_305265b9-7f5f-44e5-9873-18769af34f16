import sys

def manhattan_distance(x1, y1, x2, y2):
    return abs(x1 - x2) + abs(y1 - y2)

def get_adjacent_tiles(x, y, width, height):
    """Return list of orthogonally adjacent tile coordinates."""
    directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]  # Down, Up, Right, Left
    adjacent = []
    for dx, dy in directions:
        nx, ny = x + dx, y + dy
        if 0 <= nx < width and 0 <= ny < height:
            adjacent.append((nx, ny))
    return adjacent

def find_closest_cover_position(agent, grid, width, height):
    """Find the closest tile adjacent to the highest cover (one move away)."""
    current_x, current_y = agent['x'], agent['y']
    adjacent_tiles = get_adjacent_tiles(current_x, current_y, width, height)
    best_cover = 0
    best_pos = None
    for x, y in adjacent_tiles:
        # Skip if the tile is a cover tile (impassable)
        tile = next((t for t in grid if t['x'] == x and t['y'] == y), None)
        if tile and tile['tile_type'] > 0:
            continue  # Cannot move onto cover tiles
        # Check adjacent tiles for cover
        cover_adjacent = get_adjacent_tiles(x, y, width, height)
        max_cover = 0
        for cx, cy in cover_adjacent:
            cover_tile = next((t for t in grid if t['x'] == cx and t['y'] == cy), None)
            if cover_tile and cover_tile['tile_type'] > max_cover:
                max_cover = cover_tile['tile_type']
        if max_cover > best_cover:
            best_cover = max_cover
            best_pos = (x, y)
        elif max_cover == best_cover and best_pos is None:
            best_pos = (x, y)  # Default to first valid position if tied
    return best_pos, best_cover

def get_effective_cover_value(shooter_x, shooter_y, enemy, grid, width, height):
    """Calculate effective cover for an enemy from the shooter's perspective."""
    enemy_x, enemy_y = enemy['x'], enemy['y']
    adjacent = get_adjacent_tiles(enemy_x, enemy_y, width, height)
    max_cover = 0
    cover_positions = []
    
    # If shooter and enemy are adjacent to the same cover, cover is ignored
    shooter_adjacent = get_adjacent_tiles(shooter_x, shooter_y, width, height)
    for x, y in adjacent:
        tile = next((t for t in grid if t['x'] == x and t['y'] == y), None)
        if tile and tile['tile_type'] > 0:
            if (x, y) in shooter_adjacent:
                continue  # Cover ignored if both are adjacent
            # Check if the cover tile lies directly on the shot path
            dx = enemy_x - shooter_x
            dy = enemy_y - shooter_y
            is_between = False
            if dx != 0 or dy != 0:
                if abs(dx) >= abs(dy):  # Primarily horizontal shot
                    if (dx > 0 and x < enemy_x and x >= shooter_x and y == enemy_y) or \
                       (dx < 0 and x > enemy_x and x <= shooter_x and y == enemy_y):
                        is_between = True
                else:  # Primarily vertical shot
                    if (dy > 0 and y < enemy_y and y >= shooter_y and x == enemy_x) or \
                       (dy < 0 and y > enemy_y and y <= shooter_y and x == enemy_x):
                        is_between = True
            if is_between:
                max_cover = max(max_cover, tile['tile_type'])
                cover_positions.append((x, y, tile['tile_type']))
    
    return max_cover, cover_positions

def calculate_effective_wetness(shooter_x, shooter_y, enemy, grid, width, height, soaking_power, optimal_range):
    """Calculate the effective wetness applied to an enemy."""
    distance = manhattan_distance(shooter_x, shooter_y, enemy['x'], enemy['y'])
    cover_value, cover_positions = get_effective_cover_value(shooter_x, shooter_y, enemy, grid, width, height)
    
    # Cover multiplier
    cover_multiplier = 1.0
    if cover_value == 1:
        cover_multiplier = 0.5  # Low cover: 50% damage
    elif cover_value == 2:
        cover_multiplier = 0.25  # High cover: 25% damage
    
    # Distance multiplier
    if distance > 2 * optimal_range:
        distance_multiplier = 0.0  # Shots beyond 2 * optimal_range fail
    elif distance > optimal_range:
        distance_multiplier = 0.5  # Reduced damage beyond optimal_range
    else:
        distance_multiplier = 1.0  # Full damage within optimal_range
    
    # Effective wetness
    effective_wetness = soaking_power * cover_multiplier * distance_multiplier
    return effective_wetness, cover_value, distance, cover_positions

def find_best_target_enemy(shooter_x, shooter_y, enemies, grid, width, height, optimal_range, soaking_power, taken_enemies):
    """Rank enemies by effective wetness and filter by distance."""
    if not enemies:
        return None
    # Rank all enemies by effective wetness
    enemy_data = []
    for enemy in enemies:
        if enemy['agent_id'] in taken_enemies:
            continue
        wetness, cover_value, distance, cover_pos = calculate_effective_wetness(
            shooter_x, shooter_y, enemy, grid, width, height, soaking_power, optimal_range
        )
        # Filter by distance: within (0.5 * map_width) + 1
        max_distance = (width * 0.5) + 1
        if distance <= max_distance:
            enemy_data.append((enemy, wetness, cover_value, distance, cover_pos))
    
    if not enemy_data:
        return None
    
    # Sort by effective wetness (descending), then distance (ascending)
    enemy_data.sort(key=lambda x: (-x[1], x[3]))
    print(f"DEBUG: Agent at ({shooter_x}, {shooter_y}) enemy options: {[(e[0]['agent_id'], e[1], e[2], e[3], e[4]) for e in enemy_data]}", file=sys.stderr)
    return enemy_data[0][0] if enemy_data else None

# Read initialization input
my_id = int(input())
agent_data_count = int(input())
agents_init = []
for _ in range(agent_data_count):
    agent_id, player, shoot_cooldown, optimal_range, soaking_power, splash_bombs = map(int, input().split())
    agents_init.append({
        'agent_id': agent_id,
        'player': player,
        'shoot_cooldown': shoot_cooldown,
        'optimal_range': optimal_range,
        'soaking_power': soaking_power,
        'splash_bombs': splash_bombs
    })
width, height = map(int, input().split())
grid = []
for i in range(height):
    inputs = input().split()
    for j in range(width):
        x = int(inputs[3*j])
        y = int(inputs[3*j+1])
        tile_type = int(inputs[3*j+2])
        grid.append({'x': x, 'y': y, 'tile_type': tile_type})

# Single-turn game loop
agent_count = int(input())
agents = []
for _ in range(agent_count):
    agent_id, x, y, cooldown, splash_bombs, wetness = map(int, input().split())
    agents.append({
        'agent_id': agent_id,
        'x': x,
        'y': y,
        'cooldown': cooldown,
        'splash_bombs': splash_bombs,
        'wetness': wetness
    })
my_agent_count = int(input())

# Find my agents and enemy agents
my_agents = [agent for agent in agents if any(init_agent['agent_id'] == agent['agent_id'] and init_agent['player'] == my_id for init_agent in agents_init)]
enemy_agents = [agent for agent in agents if any(init_agent['agent_id'] == agent['agent_id'] and init_agent['player'] != my_id for init_agent in agents_init)]

# Debug: Print initial state
print(f"DEBUG: My agents: {my_agents}", file=sys.stderr)
print(f"DEBUG: Enemy agents: {enemy_agents}", file=sys.stderr)
print(f"DEBUG: Grid cover tiles: {[t for t in grid if t['tile_type'] > 0]}", file=sys.stderr)

# Track taken enemies to ensure unique targets
taken_enemies = set()

# Generate commands for each of my agents
for my_agent in sorted(my_agents, key=lambda x: x['agent_id']):
    command = f"{my_agent['agent_id']}"
    
    # Find the closest cover position
    best_pos, best_cover = find_closest_cover_position(my_agent, grid, width, height)
    print(f"DEBUG: Agent {my_agent['agent_id']} best cover pos: {best_pos}, cover value: {best_cover}", file=sys.stderr)
    
    # Move to the best cover position if not already there
    if best_pos and (my_agent['x'], my_agent['y']) != best_pos:
        move_x, move_y = best_pos
        command += f";MOVE {move_x} {move_y}"
        agent_x, agent_y = move_x, move_y
    else:
        agent_x, agent_y = my_agent['x'], my_agent['y']
    
    # Find the enemy with highest effective wetness within distance limit
    init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == my_agent['agent_id'])
    target_enemy = find_best_target_enemy(
        agent_x, agent_y, enemy_agents, grid, width, height,
        init_agent['optimal_range'], init_agent['soaking_power'], taken_enemies
    )
    print(f"DEBUG: Agent {my_agent['agent_id']} target enemy: {target_enemy['agent_id'] if target_enemy else None}", file=sys.stderr)
    
    # Shoot the target enemy if within range and can shoot
    if target_enemy and my_agent['cooldown'] == 0:
        effective_wetness, cover_value, distance, cover_pos = calculate_effective_wetness(
            agent_x, agent_y, target_enemy, grid, width, height,
            init_agent['soaking_power'], init_agent['optimal_range']
        )
        print(f"DEBUG: Agent {my_agent['agent_id']} distance to target: {distance}, max range: {2 * init_agent['optimal_range']}, effective cover: {cover_value}, effective wetness: {effective_wetness}, cover positions: {cover_pos}", file=sys.stderr)
        if distance <= 2 * init_agent['optimal_range']:
            command += f";SHOOT {target_enemy['agent_id']}"
            taken_enemies.add(target_enemy['agent_id'])  # Mark enemy as taken
        else:
            command += ";HUNKER_DOWN"
    else:
        command += ";HUNKER_DOWN"
    
    # Add a debug message
    command += f";MESSAGE Agent {my_agent['agent_id']} cover {best_cover}"
    
    print(command)