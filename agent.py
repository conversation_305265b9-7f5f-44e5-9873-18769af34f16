import sys
from collections import deque

def manhattan_distance(x1, y1, x2, y2):
    return abs(x1 - x2) + abs(y1 - y2)

def get_adjacent_tiles(x, y, width, height):
    """Return list of orthogonally adjacent tile coordinates."""
    directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]  # Down, Up, Right, Left
    adjacent = []
    for dx, dy in directions:
        nx, ny = x + dx, y + dy
        if 0 <= nx < width and 0 <= ny < height:
            adjacent.append((nx, ny))
    return adjacent

def get_splash_area(x, y, width, height):
    """Return list of all tiles affected by splash bomb (target + 8 surrounding tiles)."""
    splash_tiles = [(x, y)]  # Target tile
    # Add all 8 surrounding tiles (orthogonal + diagonal)
    directions = [(0, 1), (0, -1), (1, 0), (-1, 0), (1, 1), (1, -1), (-1, 1), (-1, -1)]
    for dx, dy in directions:
        nx, ny = x + dx, y + dy
        if 0 <= nx < width and 0 <= ny < height:
            splash_tiles.append((nx, ny))
    return splash_tiles

def can_move_to_tile(x, y, grid, agents):
    """Check if a tile is passable (no cover, no other agents)."""
    # Check for cover
    tile = next((t for t in grid if t['x'] == x and t['y'] == y), None)
    if tile and tile['tile_type'] > 0:
        return False
    # Check for other agents
    for agent in agents:
        if agent['x'] == x and agent['y'] == y:
            return False
    return True

def find_reachable_tiles(start_x, start_y, grid, agents, width, height, max_distance=None):
    """Find all tiles reachable from start position using BFS."""
    visited = set()
    reachable = {}  # (x, y) -> distance
    queue = deque([(start_x, start_y, 0)])
    visited.add((start_x, start_y))
    reachable[(start_x, start_y)] = 0

    while queue:
        x, y, dist = queue.popleft()

        if max_distance and dist >= max_distance:
            continue

        for nx, ny in get_adjacent_tiles(x, y, width, height):
            if (nx, ny) not in visited and can_move_to_tile(nx, ny, grid, agents):
                visited.add((nx, ny))
                reachable[(nx, ny)] = dist + 1
                queue.append((nx, ny, dist + 1))

    return reachable

def is_safe_splash_target(target_x, target_y, my_agents, width, height):
    """Check if throwing a splash bomb at target won't hit any of our agents."""
    splash_area = get_splash_area(target_x, target_y, width, height)
    for agent in my_agents:
        if (agent['x'], agent['y']) in splash_area:
            return False
    return True

def find_best_splash_targets(agent_x, agent_y, enemy_agents, my_agents, width, height):
    """Find the best splash bomb targets that maximize enemy damage while avoiding friendly fire."""
    max_throw_range = 4
    best_targets = []

    print(f"DEBUG: Finding targets for agent at ({agent_x}, {agent_y})", file=sys.stderr)

    # Consider all possible throw targets within range
    for target_x in range(max(0, agent_x - max_throw_range), min(width, agent_x + max_throw_range + 1)):
        for target_y in range(max(0, agent_y - max_throw_range), min(height, agent_y + max_throw_range + 1)):
            distance = manhattan_distance(agent_x, agent_y, target_x, target_y)
            if distance > max_throw_range:
                continue

            # Check if this target is safe (won't hit our agents)
            if not is_safe_splash_target(target_x, target_y, my_agents, width, height):
                continue

            # Count how many enemies would be hit
            splash_area = get_splash_area(target_x, target_y, width, height)
            enemies_hit = []
            total_enemy_wetness = 0
            for enemy in enemy_agents:
                if (enemy['x'], enemy['y']) in splash_area:
                    enemies_hit.append(enemy)
                    # Consider current wetness - prioritize enemies closer to elimination
                    total_enemy_wetness += enemy.get('wetness', 0)

            if enemies_hit:
                # Calculate total damage potential (30 wetness per enemy hit)
                total_damage = len(enemies_hit) * 30
                # Bonus for hitting enemies that are already damaged
                elimination_potential = sum(1 for enemy in enemies_hit if enemy.get('wetness', 0) + 30 >= 100)

                best_targets.append({
                    'target_pos': (target_x, target_y),
                    'distance': distance,
                    'enemies_hit': enemies_hit,
                    'total_damage': total_damage,
                    'enemy_count': len(enemies_hit),
                    'elimination_potential': elimination_potential,
                    'total_enemy_wetness': total_enemy_wetness
                })

    # Sort by: elimination potential, most enemies hit, total enemy wetness, then closest distance
    best_targets.sort(key=lambda x: (-x['elimination_potential'], -x['enemy_count'], -x['total_enemy_wetness'], x['distance']))
    return best_targets

def detect_jail_cells(grid, width, height):
    """Detect rectangular jail cells formed by cover tiles."""
    cells = []
    cover_tiles = {(t['x'], t['y']) for t in grid if t['tile_type'] > 0}

    # Look for rectangular patterns of cover tiles
    for x in range(0, width - 4):
        for y in range(0, height - 4):
            # Check for 5x5 cover pattern (4x4 walls with 3x3 interior)
            is_cell = True
            # Check top and bottom walls
            for i in range(5):
                if (x + i, y) not in cover_tiles or (x + i, y + 4) not in cover_tiles:
                    is_cell = False
                    break
            # Check left and right walls
            if is_cell:
                for i in range(1, 4):
                    if (x, y + i) not in cover_tiles or (x + 4, y + i) not in cover_tiles:
                        is_cell = False
                        break

            if is_cell:
                # Interior coordinates
                interior = [(x + 1 + i, y + 1 + j) for i in range(3) for j in range(3)]
                cells.append({
                    'bounds': (x, y, x + 4, y + 4),
                    'interior': interior,
                    'center': (x + 2, y + 2)
                })

    return cells

def find_agent_cell(agent, cells):
    """Find which cell an agent is in, if any."""
    for cell in cells:
        if (agent['x'], agent['y']) in cell['interior']:
            return cell
    return None

def find_safe_throw_position_in_cell(enemies_in_cell, cell):
    """Find position within cell where agent can throw and survive."""
    center_x, center_y = cell['center']

    # Try each position in the cell interior
    for pos_x, pos_y in cell['interior']:
        # Check if throwing at center from this position would hit all enemies but not us
        splash_area = get_splash_area(center_x, center_y, 20, 20)  # Use large bounds

        # Check if all enemies in cell would be hit
        all_enemies_hit = True
        for enemy in enemies_in_cell:
            if (enemy['x'], enemy['y']) not in splash_area:
                all_enemies_hit = False
                break

        # Check if we would be safe at this position
        agent_safe = (pos_x, pos_y) not in splash_area

        if all_enemies_hit and agent_safe:
            return (pos_x, pos_y), (center_x, center_y)

    return None, None

def find_cells_with_only_enemies(cells, enemy_agents, my_agents):
    """Find cells that contain only enemies (no friendly agents)."""
    target_cells = []

    for cell in cells:
        enemies_in_cell = [e for e in enemy_agents if (e['x'], e['y']) in cell['interior']]
        friendlies_in_cell = [a for a in my_agents if (a['x'], a['y']) in cell['interior']]

        if enemies_in_cell and not friendlies_in_cell:
            target_cells.append({
                'cell': cell,
                'enemies': enemies_in_cell,
                'enemy_count': len(enemies_in_cell)
            })

    return target_cells

def find_optimal_throw_position(agent, enemy_agents, my_agents, grid, width, height):
    """Find the best position for the agent to move to for optimal splash bomb throwing."""
    # Detect jail cells first
    cells = detect_jail_cells(grid, width, height)
    agent_cell = find_agent_cell(agent, cells)

    print(f"DEBUG: Agent {agent['agent_id']} detected {len(cells)} cells, agent in cell: {agent_cell is not None}", file=sys.stderr)

    if agent_cell:
        # Agent is trapped in a cell - find safe position to eliminate enemies in same cell
        enemies_in_cell = [e for e in enemy_agents if (e['x'], e['y']) in agent_cell['interior']]
        print(f"DEBUG: Agent {agent['agent_id']} in cell with {len(enemies_in_cell)} enemies", file=sys.stderr)

        if enemies_in_cell:
            safe_pos, throw_target = find_safe_throw_position_in_cell(enemies_in_cell, agent_cell)
            if safe_pos and throw_target:
                # Return the safe position and create a target list
                targets = [{
                    'target_pos': throw_target,
                    'distance': manhattan_distance(safe_pos[0], safe_pos[1], throw_target[0], throw_target[1]),
                    'enemies_hit': enemies_in_cell,
                    'total_damage': len(enemies_in_cell) * 30,
                    'enemy_count': len(enemies_in_cell),
                    'elimination_potential': len(enemies_in_cell),  # All should be eliminated
                    'total_enemy_wetness': sum(e.get('wetness', 0) for e in enemies_in_cell)
                }]
                return safe_pos, targets
    else:
        # Agent is outside cells - look for cells with only enemies to target
        enemy_only_cells = find_cells_with_only_enemies(cells, enemy_agents, my_agents)
        print(f"DEBUG: Agent {agent['agent_id']} found {len(enemy_only_cells)} cells with only enemies", file=sys.stderr)

        if enemy_only_cells:
            # Find the best cell to target and position to throw from
            best_cell_target = None
            best_throw_pos = None
            best_score = 0

            for cell_info in enemy_only_cells:
                cell = cell_info['cell']
                center_x, center_y = cell['center']

                # Find positions around the cell perimeter where we can throw to center
                for pos_x, pos_y in reachable.items() if 'reachable' in locals() else []:
                    distance = manhattan_distance(pos_x, pos_y, center_x, center_y)
                    if distance <= 4:
                        # Score based on number of enemies and distance
                        score = cell_info['enemy_count'] * 1000 - distance * 10
                        if score > best_score:
                            best_score = score
                            best_cell_target = cell_info
                            best_throw_pos = (pos_x, pos_y)

            if best_cell_target and best_throw_pos:
                cell = best_cell_target['cell']
                center_x, center_y = cell['center']
                targets = [{
                    'target_pos': (center_x, center_y),
                    'distance': manhattan_distance(best_throw_pos[0], best_throw_pos[1], center_x, center_y),
                    'enemies_hit': best_cell_target['enemies'],
                    'total_damage': len(best_cell_target['enemies']) * 30,
                    'enemy_count': len(best_cell_target['enemies']),
                    'elimination_potential': len(best_cell_target['enemies']),
                    'total_enemy_wetness': sum(e.get('wetness', 0) for e in best_cell_target['enemies'])
                }]
                return best_throw_pos, targets

    # Standard pathfinding for agents outside cells or when no special cell strategy applies
    other_agents = [a for a in my_agents if a['agent_id'] != agent['agent_id']]
    reachable = find_reachable_tiles(agent['x'], agent['y'], grid, other_agents, width, height, max_distance=3)

    best_position = None
    best_targets = []
    best_score = 0

    # Try each reachable position
    for (pos_x, pos_y), move_distance in reachable.items():
        # Find best splash targets from this position
        targets = find_best_splash_targets(pos_x, pos_y, enemy_agents, my_agents, width, height)

        if targets:
            # Score based on best target from this position
            best_target = targets[0]
            # Prefer positions that can hit more enemies, with elimination potential, shorter move distance
            score = (best_target['elimination_potential'] * 10000 +
                    best_target['enemy_count'] * 1000 +
                    best_target['total_damage'] - move_distance * 10)

            if score > best_score:
                best_score = score
                best_position = (pos_x, pos_y)
                best_targets = targets

    return best_position, best_targets

def execute_balanced_strategy(my_agents, enemy_agents, grid, width, height, agents_init):
    """Execute the balanced strategy (original full-game strategy)."""
    taken_enemies = set()
    agent_commands = {}

    for my_agent in sorted(my_agents, key=lambda x: x['agent_id']):
        command = f"{my_agent['agent_id']}"

        print(f"DEBUG: Processing agent {my_agent['agent_id']} at ({my_agent['x']}, {my_agent['y']}) with {my_agent['splash_bombs']} bombs, wetness: {my_agent['wetness']}", file=sys.stderr)

        # Find the best position for this agent (considering cover and territory)
        prioritize_cover = my_agent['wetness'] >= 30  # Prioritize cover if taking damage
        best_pos, best_cover_value = find_best_position(my_agent, grid, width, height, my_agents, enemy_agents, prioritize_cover)
        print(f"DEBUG: Agent {my_agent['agent_id']} best pos: {best_pos}, cover value: {best_cover_value}, prioritize_cover: {prioritize_cover}", file=sys.stderr)

        # Determine agent's current strategy based on state
        agent_x, agent_y = my_agent['x'], my_agent['y']
        moved_this_turn = False

        # Strategy 1: If agent has high wetness (>= 50), prioritize survival
        if my_agent['wetness'] >= 50:
            print(f"DEBUG: Agent {my_agent['agent_id']} has high wetness ({my_agent['wetness']}), prioritizing survival", file=sys.stderr)
            if best_pos and (agent_x, agent_y) != best_pos:
                move_x, move_y = best_pos
                command += f";MOVE {move_x} {move_y}"
                agent_x, agent_y = move_x, move_y
                moved_this_turn = True
            command += ";HUNKER_DOWN"

        # Strategy 2: Use splash bombs if available and can hit enemies safely
        elif my_agent['splash_bombs'] > 0:
            # Find best splash targets from current position
            current_throw_targets = find_best_splash_targets(agent_x, agent_y, enemy_agents, my_agents, width, height)

            # Filter out targets that hit enemies already targeted by other agents
            available_targets = []
            for target in current_throw_targets:
                enemy_ids = {enemy['agent_id'] for enemy in target['enemies_hit']}
                if not enemy_ids.intersection(taken_enemies):
                    available_targets.append(target)

            if available_targets:
                best_target = available_targets[0]
                target_x, target_y = best_target['target_pos']
                throw_distance = manhattan_distance(agent_x, agent_y, target_x, target_y)

                if throw_distance <= 4:
                    command += f";THROW {target_x} {target_y}"
                    # Mark these enemies as taken
                    for enemy in best_target['enemies_hit']:
                        taken_enemies.add(enemy['agent_id'])
                    print(f"DEBUG: Agent {my_agent['agent_id']} throwing at ({target_x}, {target_y}), distance: {throw_distance}, hitting {best_target['enemy_count']} enemies", file=sys.stderr)
                else:
                    # Move to better position for throwing
                    optimal_pos, _ = find_optimal_throw_position(my_agent, enemy_agents, my_agents, grid, width, height)
                    if optimal_pos and (agent_x, agent_y) != optimal_pos:
                        move_x, move_y = optimal_pos
                        command += f";MOVE {move_x} {move_y}"
                        agent_x, agent_y = move_x, move_y
                        moved_this_turn = True
                    command += ";HUNKER_DOWN"
            else:
                # No good throw targets, try shooting or move to better position
                self_shoot_strategy = True
                if not moved_this_turn and self_shoot_strategy:
                    # Move to better position for future actions
                    if best_pos and (my_agent['x'], my_agent['y']) != best_pos:
                        move_x, move_y = best_pos
                        command += f";MOVE {move_x} {move_y}"
                        agent_x, agent_y = move_x, move_y
                        moved_this_turn = True

                # Try shooting if not moved or no good move
                if not moved_this_turn and my_agent['cooldown'] == 0:
                    init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == my_agent['agent_id'])
                    target_enemy = find_best_target_enemy(
                        agent_x, agent_y, enemy_agents, grid, width, height,
                        init_agent['optimal_range'], init_agent['soaking_power'], taken_enemies
                    )

                    if target_enemy:
                        command += f";SHOOT {target_enemy['agent_id']}"
                        taken_enemies.add(target_enemy['agent_id'])
                        print(f"DEBUG: Agent {my_agent['agent_id']} shooting enemy {target_enemy['agent_id']}", file=sys.stderr)
                    else:
                        command += ";HUNKER_DOWN"
                else:
                    command += ";HUNKER_DOWN"

        # Strategy 3: No splash bombs, focus on shooting and positioning
        else:
            if not moved_this_turn and my_agent['cooldown'] == 0:
                init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == my_agent['agent_id'])
                target_enemy = find_best_target_enemy(
                    agent_x, agent_y, enemy_agents, grid, width, height,
                    init_agent['optimal_range'], init_agent['soaking_power'], taken_enemies
                )

                if target_enemy:
                    # Check if we should move to better shooting position
                    effective_wetness, cover_value, distance, cover_pos = calculate_effective_wetness(
                        agent_x, agent_y, target_enemy, grid, width, height,
                        init_agent['soaking_power'], init_agent['optimal_range']
                    )

                    if effective_wetness > 0 and distance <= 2 * init_agent['optimal_range']:
                        command += f";SHOOT {target_enemy['agent_id']}"
                        taken_enemies.add(target_enemy['agent_id'])
                        print(f"DEBUG: Agent {my_agent['agent_id']} shooting enemy {target_enemy['agent_id']}, effective wetness: {effective_wetness}", file=sys.stderr)
                    else:
                        # Move to better position
                        if best_pos and (agent_x, agent_y) != best_pos:
                            move_x, move_y = best_pos
                            command += f";MOVE {move_x} {move_y}"
                            moved_this_turn = True
                        command += ";HUNKER_DOWN"
                else:
                    # No targets, move to better position
                    if best_pos and (agent_x, agent_y) != best_pos:
                        move_x, move_y = best_pos
                        command += f";MOVE {move_x} {move_y}"
                        moved_this_turn = True
                    command += ";HUNKER_DOWN"
            else:
                # On cooldown or already moved, just hunker down
                command += ";HUNKER_DOWN"

        # Add a debug message
        status = f"W:{my_agent['wetness']} B:{my_agent['splash_bombs']} C:{my_agent['cooldown']}"
        command += f";MESSAGE {status}"

        agent_commands[my_agent['agent_id']] = command

    return agent_commands

def find_formation_positions(my_agents, target_enemy, grid, width, height):
    """Find positions for agents to form a line facing the target enemy."""
    if not target_enemy:
        return {}

    target_x, target_y = target_enemy['x'], target_enemy['y']

    # Calculate the center point of our agents
    center_x = sum(agent['x'] for agent in my_agents) // len(my_agents)
    center_y = sum(agent['y'] for agent in my_agents) // len(my_agents)

    # Determine direction from center to target
    dx = target_x - center_x
    dy = target_y - center_y

    # Normalize direction and find perpendicular for line formation
    if abs(dx) > abs(dy):
        # Horizontal approach - form vertical line
        line_direction = (0, 1) if dx > 0 else (0, -1)
        approach_direction = (1, 0) if dx > 0 else (-1, 0)
    else:
        # Vertical approach - form horizontal line
        line_direction = (1, 0) if dy > 0 else (-1, 0)
        approach_direction = (0, 1) if dy > 0 else (0, -1)

    # Find optimal distance from target (slightly outside optimal range)
    optimal_distance = 4  # Just outside typical optimal range of 3

    # Calculate base position for formation
    base_x = target_x - approach_direction[0] * optimal_distance
    base_y = target_y - approach_direction[1] * optimal_distance

    # Generate formation positions
    formation_positions = {}
    num_agents = len(my_agents)
    start_offset = -(num_agents - 1) // 2

    for i, agent in enumerate(sorted(my_agents, key=lambda x: x['agent_id'])):
        offset = start_offset + i
        pos_x = base_x + line_direction[0] * offset * 2  # 2 spaces apart
        pos_y = base_y + line_direction[1] * offset * 2

        # Ensure position is within bounds and not on cover
        pos_x = max(0, min(width - 1, pos_x))
        pos_y = max(0, min(height - 1, pos_y))

        # Check if position is valid (not on cover)
        tile = next((t for t in grid if t['x'] == pos_x and t['y'] == pos_y), None)
        if tile and tile['tile_type'] > 0:
            # Find nearby valid position
            for dx in range(-2, 3):
                for dy in range(-2, 3):
                    alt_x, alt_y = pos_x + dx, pos_y + dy
                    if 0 <= alt_x < width and 0 <= alt_y < height:
                        alt_tile = next((t for t in grid if t['x'] == alt_x and t['y'] == alt_y), None)
                        if not alt_tile or alt_tile['tile_type'] == 0:
                            pos_x, pos_y = alt_x, alt_y
                            break
                if not tile or tile['tile_type'] == 0:
                    break

        formation_positions[agent['agent_id']] = (pos_x, pos_y)

    return formation_positions

def calculate_territory_split(my_agents, enemy_agents, _width, _height):
    """Calculate rough territory control using center of mass."""
    my_center_x = sum(agent['x'] for agent in my_agents) / len(my_agents)
    my_center_y = sum(agent['y'] for agent in my_agents) / len(my_agents)

    enemy_center_x = sum(agent['x'] for agent in enemy_agents) / len(enemy_agents)
    enemy_center_y = sum(agent['y'] for agent in enemy_agents) / len(enemy_agents)

    # Calculate midpoint between centers
    mid_x = (my_center_x + enemy_center_x) / 2
    mid_y = (my_center_y + enemy_center_y) / 2

    return (my_center_x, my_center_y), (enemy_center_x, enemy_center_y), (mid_x, mid_y)

def find_safe_attack_positions(my_agents, target_enemy, enemy_agents, grid, width, height, agents_init):
    """Find positions where agents can attack target while staying safe from other enemies."""
    target_x, target_y = target_enemy['x'], target_enemy['y']
    safe_positions = {}

    # Get optimal ranges for our agents
    agent_ranges = {}
    for agent in my_agents:
        init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == agent['agent_id'])
        agent_ranges[agent['agent_id']] = init_agent['optimal_range']

    # Get enemy ranges for safety calculations
    enemy_ranges = {}
    for enemy in enemy_agents:
        init_enemy = next(init_a for init_a in agents_init if init_a['agent_id'] == enemy['agent_id'])
        enemy_ranges[enemy['agent_id']] = init_enemy['optimal_range']

    # For each agent, find positions that can attack target but stay safe from other enemies
    for agent in my_agents:
        agent_optimal_range = agent_ranges[agent['agent_id']]
        max_attack_range = 2 * agent_optimal_range  # Maximum effective range

        best_positions = []

        # Check positions in a circle around the target
        for dx in range(-max_attack_range, max_attack_range + 1):
            for dy in range(-max_attack_range, max_attack_range + 1):
                pos_x = target_x + dx
                pos_y = target_y + dy

                # Must be within map bounds
                if not (0 <= pos_x < width and 0 <= pos_y < height):
                    continue

                # Must be within attack range of target
                distance_to_target = manhattan_distance(pos_x, pos_y, target_x, target_y)
                if distance_to_target > max_attack_range:
                    continue

                # Must not be on cover
                tile = next((t for t in grid if t['x'] == pos_x and t['y'] == pos_y), None)
                if tile and tile['tile_type'] > 0:
                    continue

                # Calculate safety from other enemies
                min_enemy_distance = float('inf')
                for enemy in enemy_agents:
                    if enemy['agent_id'] == target_enemy['agent_id']:
                        continue  # Skip the target enemy

                    enemy_distance = manhattan_distance(pos_x, pos_y, enemy['x'], enemy['y'])
                    enemy_optimal_range = enemy_ranges.get(enemy['agent_id'], 3)  # Default to 3

                    # We want to stay outside enemy optimal range for safety
                    safety_distance = enemy_optimal_range + 1
                    if enemy_distance < safety_distance:
                        min_enemy_distance = -1  # Mark as unsafe
                        break
                    min_enemy_distance = min(min_enemy_distance, enemy_distance)

                if min_enemy_distance > 0:  # Position is safe
                    # Check for nearby cover
                    cover_bonus = 0
                    adjacent_tiles = get_adjacent_tiles(pos_x, pos_y, width, height)
                    for adj_x, adj_y in adjacent_tiles:
                        adj_tile = next((t for t in grid if t['x'] == adj_x and t['y'] == adj_y), None)
                        if adj_tile and adj_tile['tile_type'] > 0:
                            cover_bonus = adj_tile['tile_type'] * 10
                            break

                    # Score position: prefer positions where we can deal ANY damage
                    if distance_to_target <= agent_optimal_range:
                        range_score = 100  # Optimal range - maximum damage
                    elif distance_to_target <= 2 * agent_optimal_range:
                        range_score = 80   # Extended range - still effective
                    else:
                        range_score = 20   # Long range - minimal damage but still something

                    safety_score = min(min_enemy_distance, 10) * 5  # Cap safety bonus
                    total_score = range_score + cover_bonus + safety_score

                    best_positions.append({
                        'pos': (pos_x, pos_y),
                        'score': total_score,
                        'distance_to_target': distance_to_target,
                        'cover_bonus': cover_bonus,
                        'safety_distance': min_enemy_distance
                    })

        # Sort by score and store all good positions
        if best_positions:
            best_positions.sort(key=lambda x: -x['score'])
            safe_positions[agent['agent_id']] = best_positions  # Store all options
            print(f"DEBUG: Agent {agent['agent_id']} found {len(best_positions)} attack positions, best: {best_positions[0]['pos']}, score: {best_positions[0]['score']}", file=sys.stderr)
        else:
            # No safe position found, find ANY position where we can shoot (even if not safe)
            fallback_positions = []
            for dx in range(-max_attack_range, max_attack_range + 1):
                for dy in range(-max_attack_range, max_attack_range + 1):
                    pos_x = target_x + dx
                    pos_y = target_y + dy

                    if not (0 <= pos_x < width and 0 <= pos_y < height):
                        continue

                    distance_to_target = manhattan_distance(pos_x, pos_y, target_x, target_y)
                    if distance_to_target > max_attack_range:
                        continue

                    # Must not be on cover
                    tile = next((t for t in grid if t['x'] == pos_x and t['y'] == pos_y), None)
                    if tile and tile['tile_type'] > 0:
                        continue

                    # Any position where we can deal damage
                    fallback_positions.append({
                        'pos': (pos_x, pos_y),
                        'score': 10,  # Low score but better than nothing
                        'distance_to_target': distance_to_target,
                        'cover_bonus': 0,
                        'safety_distance': 0
                    })

            if fallback_positions:
                # Sort by distance to target (prefer closer)
                fallback_positions.sort(key=lambda x: x['distance_to_target'])
                safe_positions[agent['agent_id']] = fallback_positions
                print(f"DEBUG: Agent {agent['agent_id']} found {len(fallback_positions)} fallback attack positions", file=sys.stderr)
            else:
                # Absolutely no position found, use current position
                safe_positions[agent['agent_id']] = [{
                    'pos': (agent['x'], agent['y']),
                    'score': 0,
                    'distance_to_target': manhattan_distance(agent['x'], agent['y'], target_x, target_y),
                    'cover_bonus': 0,
                    'safety_distance': 0
                }]
                print(f"DEBUG: Agent {agent['agent_id']} no attack positions found, staying at current", file=sys.stderr)

    # Resolve position conflicts - ensure agents don't try to occupy same tile
    final_positions = {}
    used_positions = set()

    # Sort agents by priority (could be by agent ID, distance to target, etc.)
    agent_priority = sorted(my_agents, key=lambda x: x['agent_id'])

    for agent in agent_priority:
        agent_options = safe_positions[agent['agent_id']]

        # Find the best available position
        chosen_position = None
        for option in agent_options:
            if option['pos'] not in used_positions:
                chosen_position = option
                break

        if chosen_position:
            final_positions[agent['agent_id']] = chosen_position
            used_positions.add(chosen_position['pos'])
        else:
            # All preferred positions taken, use current position
            final_positions[agent['agent_id']] = {
                'pos': (agent['x'], agent['y']),
                'score': 0,
                'distance_to_target': manhattan_distance(agent['x'], agent['y'], target_x, target_y),
                'cover_bonus': 0,
                'safety_distance': 0
            }

    return final_positions

def execute_coordinated_focus_fire_strategy(my_agents, enemy_agents, grid, width, height, agents_init):
    """Execute coordinated focus fire strategy - all agents target closest enemy, shoot whenever possible."""
    agent_commands = {}

    print(f"DEBUG: Coordinated focus fire strategy with {len(my_agents)} agents", file=sys.stderr)

    if not enemy_agents:
        # No enemies, just hunker down
        for agent in my_agents:
            command = f"{agent['agent_id']};HUNKER_DOWN;MESSAGE No enemies"
            agent_commands[agent['agent_id']] = command
        return agent_commands

    # Calculate territory split
    my_center, enemy_center, _ = calculate_territory_split(my_agents, enemy_agents, width, height)
    print(f"DEBUG: Territory - Our center: ({my_center[0]:.1f}, {my_center[1]:.1f}), Enemy center: ({enemy_center[0]:.1f}, {enemy_center[1]:.1f})", file=sys.stderr)

    # Find the closest enemy to our group center
    closest_enemy = min(enemy_agents,
                       key=lambda e: manhattan_distance(my_center[0], my_center[1], e['x'], e['y']))

    print(f"DEBUG: Target enemy {closest_enemy['agent_id']} at ({closest_enemy['x']}, {closest_enemy['y']})", file=sys.stderr)

    # Find safe attack positions for all agents
    safe_positions = find_safe_attack_positions(my_agents, closest_enemy, enemy_agents, grid, width, height, agents_init)

    # Process each agent - PRIORITY: shoot if possible, move only if necessary
    for agent in sorted(my_agents, key=lambda x: x['agent_id']):
        command = f"{agent['agent_id']}"
        agent_x, agent_y = agent['x'], agent['y']
        init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == agent['agent_id'])

        current_distance = manhattan_distance(agent_x, agent_y, closest_enemy['x'], closest_enemy['y'])

        print(f"DEBUG: Agent {agent['agent_id']} at ({agent_x}, {agent_y}), distance to target: {current_distance}", file=sys.stderr)

        # STEP 1: Check if agent can shoot from current position
        can_shoot_now = False
        if agent['cooldown'] == 0 and current_distance <= 2 * init_agent['optimal_range']:
            effective_wetness, _, _, _ = calculate_effective_wetness(
                agent_x, agent_y, closest_enemy, grid, width, height,
                init_agent['soaking_power'], init_agent['optimal_range']
            )
            if effective_wetness > 0:
                can_shoot_now = True
                command += f";SHOOT {closest_enemy['agent_id']}"
                print(f"DEBUG: Agent {agent['agent_id']} shooting from current position, effective wetness: {effective_wetness}", file=sys.stderr)

        # STEP 2: If can't shoot, try splash bomb from current position
        if not can_shoot_now and agent['splash_bombs'] > 0 and current_distance <= 4:
            splash_targets = find_best_splash_targets(agent_x, agent_y, [closest_enemy], my_agents, width, height)
            if splash_targets:
                best_target = splash_targets[0]
                target_x, target_y = best_target['target_pos']
                command += f";THROW {target_x} {target_y}"
                print(f"DEBUG: Agent {agent['agent_id']} throwing splash bomb from current position at ({target_x}, {target_y})", file=sys.stderr)
                can_shoot_now = True

        # STEP 3: If still can't attack, need to move closer
        if not can_shoot_now:
            # Check if agent is critically damaged - prioritize survival
            if agent['wetness'] >= 70:
                cover_pos, _ = find_closest_cover_position(agent, grid, width, height)
                if cover_pos and (agent_x, agent_y) != cover_pos:
                    move_x, move_y = cover_pos
                    command += f";MOVE {move_x} {move_y}"
                    print(f"DEBUG: Agent {agent['agent_id']} survival mode: moving to cover ({move_x}, {move_y})", file=sys.stderr)

                    # Try to shoot from new position after moving
                    new_distance = manhattan_distance(move_x, move_y, closest_enemy['x'], closest_enemy['y'])
                    if agent['cooldown'] == 0 and new_distance <= 2 * init_agent['optimal_range']:
                        effective_wetness, _, _, _ = calculate_effective_wetness(
                            move_x, move_y, closest_enemy, grid, width, height,
                            init_agent['soaking_power'], init_agent['optimal_range']
                        )
                        if effective_wetness > 0:
                            command += f";SHOOT {closest_enemy['agent_id']}"
                            print(f"DEBUG: Agent {agent['agent_id']} shooting after moving to cover, effective wetness: {effective_wetness}", file=sys.stderr)
                        else:
                            command += ";HUNKER_DOWN"
                    else:
                        command += ";HUNKER_DOWN"
                else:
                    command += ";HUNKER_DOWN"
            else:
                # Find best attack position and move there
                position_info = safe_positions.get(agent['agent_id'])
                if position_info:
                    target_pos = position_info['pos']
                    move_x, move_y = target_pos
                    command += f";MOVE {move_x} {move_y}"
                    print(f"DEBUG: Agent {agent['agent_id']} moving to attack position ({move_x}, {move_y})", file=sys.stderr)

                    # Try to shoot from new position after moving
                    new_distance = manhattan_distance(move_x, move_y, closest_enemy['x'], closest_enemy['y'])
                    if agent['cooldown'] == 0 and new_distance <= 2 * init_agent['optimal_range']:
                        effective_wetness, _, _, _ = calculate_effective_wetness(
                            move_x, move_y, closest_enemy, grid, width, height,
                            init_agent['soaking_power'], init_agent['optimal_range']
                        )
                        if effective_wetness > 0:
                            command += f";SHOOT {closest_enemy['agent_id']}"
                            print(f"DEBUG: Agent {agent['agent_id']} shooting after moving to attack position, effective wetness: {effective_wetness}", file=sys.stderr)
                        else:
                            # Try splash bomb after moving
                            if agent['splash_bombs'] > 0 and new_distance <= 4:
                                splash_targets = find_best_splash_targets(move_x, move_y, [closest_enemy], my_agents, width, height)
                                if splash_targets:
                                    best_target = splash_targets[0]
                                    target_x, target_y = best_target['target_pos']
                                    command += f";THROW {target_x} {target_y}"
                                    print(f"DEBUG: Agent {agent['agent_id']} throwing splash bomb after moving at ({target_x}, {target_y})", file=sys.stderr)
                                else:
                                    command += ";HUNKER_DOWN"
                            else:
                                command += ";HUNKER_DOWN"
                    else:
                        # Try splash bomb after moving
                        if agent['splash_bombs'] > 0 and new_distance <= 4:
                            splash_targets = find_best_splash_targets(move_x, move_y, [closest_enemy], my_agents, width, height)
                            if splash_targets:
                                best_target = splash_targets[0]
                                target_x, target_y = best_target['target_pos']
                                command += f";THROW {target_x} {target_y}"
                                print(f"DEBUG: Agent {agent['agent_id']} throwing splash bomb after moving at ({target_x}, {target_y})", file=sys.stderr)
                            else:
                                command += ";HUNKER_DOWN"
                        else:
                            command += ";HUNKER_DOWN"
                else:
                    # No good position found, just move closer to enemy
                    target_x, target_y = closest_enemy['x'], closest_enemy['y']
                    # Move one step closer
                    if agent_x < target_x:
                        move_x = agent_x + 1
                    elif agent_x > target_x:
                        move_x = agent_x - 1
                    else:
                        move_x = agent_x

                    if agent_y < target_y:
                        move_y = agent_y + 1
                    elif agent_y > target_y:
                        move_y = agent_y - 1
                    else:
                        move_y = agent_y

                    # Check if move position is valid
                    if 0 <= move_x < width and 0 <= move_y < height:
                        tile = next((t for t in grid if t['x'] == move_x and t['y'] == move_y), None)
                        if not tile or tile['tile_type'] == 0:
                            command += f";MOVE {move_x} {move_y}"
                            print(f"DEBUG: Agent {agent['agent_id']} moving closer to enemy ({move_x}, {move_y})", file=sys.stderr)
                        else:
                            command += ";HUNKER_DOWN"
                    else:
                        command += ";HUNKER_DOWN"
                    command += ";HUNKER_DOWN"

        # Add status message
        status = f"FF W:{agent['wetness']} D:{current_distance}"
        command += f";MESSAGE {status}"

        agent_commands[agent['agent_id']] = command

    return agent_commands

def calculate_map_control(my_agents, enemy_agents, width, height):
    """Calculate how much of the map each team controls using Voronoi-like analysis."""
    my_controlled = 0
    enemy_controlled = 0
    contested = 0

    for x in range(width):
        for y in range(height):
            # Find closest agent from each team
            min_my_distance = float('inf')
            min_enemy_distance = float('inf')

            for agent in my_agents:
                distance = manhattan_distance(x, y, agent['x'], agent['y'])
                min_my_distance = min(min_my_distance, distance)

            for agent in enemy_agents:
                distance = manhattan_distance(x, y, agent['x'], agent['y'])
                min_enemy_distance = min(min_enemy_distance, distance)

            # Determine control
            if min_my_distance < min_enemy_distance:
                my_controlled += 1
            elif min_enemy_distance < min_my_distance:
                enemy_controlled += 1
            else:
                contested += 1

    control_advantage = my_controlled - enemy_controlled
    return my_controlled, enemy_controlled, contested, control_advantage

def find_defensive_positions(my_agents, enemy_agents, grid, width, height, agents_init):
    """Find defensive positions that maintain spacing and cover while controlling territory."""
    positions = {}

    # Calculate current map control
    my_control, enemy_control, contested, advantage = calculate_map_control(my_agents, enemy_agents, width, height)
    print(f"DEBUG: Map control - Us: {my_control}, Enemy: {enemy_control}, Contested: {contested}, Advantage: {advantage}", file=sys.stderr)

    # Determine if we need to be aggressive or can stay defensive
    need_aggression = advantage < 3  # Push forward if we don't have 3+ tile advantage

    for agent in my_agents:
        agent_x, agent_y = agent['x'], agent['y']
        init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == agent['agent_id'])
        max_range = 2 * init_agent['optimal_range']

        best_positions = []

        # Search area around current position
        search_radius = 3 if need_aggression else 2
        for dx in range(-search_radius, search_radius + 1):
            for dy in range(-search_radius, search_radius + 1):
                pos_x = agent_x + dx
                pos_y = agent_y + dy

                # Must be within map bounds
                if not (0 <= pos_x < width and 0 <= pos_y < height):
                    continue

                # Must not be on cover
                tile = next((t for t in grid if t['x'] == pos_x and t['y'] == pos_y), None)
                if tile and tile['tile_type'] > 0:
                    continue

                # Check spacing from other friendly agents (must be 3+ tiles apart)
                # Also check against other agents' planned positions
                too_close = False
                for other_agent in my_agents:
                    if other_agent['agent_id'] == agent['agent_id']:
                        continue

                    # Check distance from other agent's current position
                    distance = manhattan_distance(pos_x, pos_y, other_agent['x'], other_agent['y'])
                    if distance < 3:
                        too_close = True
                        break

                    # Check distance from other agent's planned position (if already calculated)
                    if other_agent['agent_id'] in positions:
                        other_planned_pos = positions[other_agent['agent_id']]['pos']
                        planned_distance = manhattan_distance(pos_x, pos_y, other_planned_pos[0], other_planned_pos[1])
                        if planned_distance < 3:
                            too_close = True
                            break

                if too_close:
                    continue

                # Calculate position value
                score = 0

                # Cover bonus
                cover_bonus = 0
                adjacent_tiles = get_adjacent_tiles(pos_x, pos_y, width, height)
                for adj_x, adj_y in adjacent_tiles:
                    adj_tile = next((t for t in grid if t['x'] == adj_x and t['y'] == adj_y), None)
                    if adj_tile and adj_tile['tile_type'] > 0:
                        cover_bonus = max(cover_bonus, adj_tile['tile_type'] * 20)

                # Territory control bonus
                territory_bonus = 0
                for enemy in enemy_agents:
                    enemy_distance = manhattan_distance(pos_x, pos_y, enemy['x'], enemy['y'])
                    if enemy_distance <= max_range:
                        territory_bonus += 15  # Can threaten enemy

                # Central position bonus (prefer controlling center)
                center_x, center_y = width // 2, height // 2
                center_distance = manhattan_distance(pos_x, pos_y, center_x, center_y)
                center_bonus = max(0, 10 - center_distance)

                # Safety from enemy splash bombs
                safety_bonus = 0
                min_enemy_distance = float('inf')
                for enemy in enemy_agents:
                    enemy_distance = manhattan_distance(pos_x, pos_y, enemy['x'], enemy['y'])
                    min_enemy_distance = min(min_enemy_distance, enemy_distance)

                if min_enemy_distance > 4:  # Outside splash bomb range
                    safety_bonus = 15
                elif min_enemy_distance > 3:  # Marginal safety
                    safety_bonus = 5

                score = cover_bonus + territory_bonus + center_bonus + safety_bonus

                best_positions.append({
                    'pos': (pos_x, pos_y),
                    'score': score,
                    'cover_bonus': cover_bonus,
                    'territory_bonus': territory_bonus,
                    'safety_bonus': safety_bonus
                })

        # Sort by score and pick best
        if best_positions:
            best_positions.sort(key=lambda x: -x['score'])
            positions[agent['agent_id']] = best_positions[0]
            print(f"DEBUG: Agent {agent['agent_id']} defensive pos: {best_positions[0]['pos']}, score: {best_positions[0]['score']}", file=sys.stderr)
        else:
            # No good position found, stay put
            positions[agent['agent_id']] = {
                'pos': (agent_x, agent_y),
                'score': 0,
                'cover_bonus': 0,
                'territory_bonus': 0,
                'safety_bonus': 0
            }

    return positions, need_aggression

def find_best_target_for_defensive_strategy(my_agents, enemy_agents, agents_init):
    """Find the best target for defensive strategy - prioritize weak/exposed enemies."""
    if not enemy_agents:
        return None

    target_scores = []

    for enemy in enemy_agents:
        # Prioritize weak enemies (high wetness)
        weakness_score = enemy.get('wetness', 0)

        # Prioritize exposed enemies (far from cover)
        # This is simplified - in a real implementation we'd check actual cover positions
        exposure_score = 10  # Base exposure

        # Prioritize enemies we can actually hit
        can_hit_score = 0
        for agent in my_agents:
            init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == agent['agent_id'])
            distance = manhattan_distance(agent['x'], agent['y'], enemy['x'], enemy['y'])
            if distance <= 2 * init_agent['optimal_range']:
                can_hit_score += 20

        total_score = weakness_score + exposure_score + can_hit_score
        target_scores.append((enemy, total_score))

    # Sort by score and return best target
    target_scores.sort(key=lambda x: -x[1])
    best_target = target_scores[0][0]

    print(f"DEBUG: Best defensive target: Enemy {best_target['agent_id']} (wetness: {best_target.get('wetness', 0)})", file=sys.stderr)
    return best_target

def execute_defensive_control_strategy(my_agents, enemy_agents, grid, width, height, agents_init):
    """Execute defensive control strategy - spacing, cover, map control, selective engagement."""
    agent_commands = {}

    print(f"DEBUG: Defensive control strategy with {len(my_agents)} agents", file=sys.stderr)

    if not enemy_agents:
        # No enemies, just hunker down
        for agent in my_agents:
            command = f"{agent['agent_id']};HUNKER_DOWN;MESSAGE No enemies"
            agent_commands[agent['agent_id']] = command
        return agent_commands

    # Find defensive positions and determine aggression level
    defensive_positions, need_aggression = find_defensive_positions(my_agents, enemy_agents, grid, width, height, agents_init)

    # Find best target for coordinated attacks
    target_enemy = find_best_target_for_defensive_strategy(my_agents, enemy_agents, agents_init)

    print(f"DEBUG: Aggression needed: {need_aggression}, Target: Enemy {target_enemy['agent_id'] if target_enemy else 'None'}", file=sys.stderr)

    # Process each agent
    for agent in sorted(my_agents, key=lambda x: x['agent_id']):
        command = f"{agent['agent_id']}"
        agent_x, agent_y = agent['x'], agent['y']
        init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == agent['agent_id'])

        # Get defensive position
        position_info = defensive_positions.get(agent['agent_id'])
        target_pos = position_info['pos'] if position_info else (agent_x, agent_y)

        print(f"DEBUG: Agent {agent['agent_id']} at ({agent_x}, {agent_y}), defensive pos: {target_pos}", file=sys.stderr)

        # Check if agent is critically damaged
        if agent['wetness'] >= 60:
            # High priority on survival
            if (agent_x, agent_y) != target_pos:
                move_x, move_y = target_pos
                command += f";MOVE {move_x} {move_y}"
                print(f"DEBUG: Agent {agent['agent_id']} critical health, moving to safety ({move_x}, {move_y})", file=sys.stderr)
            command += ";HUNKER_DOWN"

        # Check if we can attack target from current position
        elif target_enemy and agent['cooldown'] == 0:
            distance_to_target = manhattan_distance(agent_x, agent_y, target_enemy['x'], target_enemy['y'])

            if distance_to_target <= 2 * init_agent['optimal_range']:
                effective_wetness, _, _, _ = calculate_effective_wetness(
                    agent_x, agent_y, target_enemy, grid, width, height,
                    init_agent['soaking_power'], init_agent['optimal_range']
                )

                if effective_wetness > 0:
                    command += f";SHOOT {target_enemy['agent_id']}"
                    print(f"DEBUG: Agent {agent['agent_id']} shooting target from current position, effective wetness: {effective_wetness}", file=sys.stderr)
                else:
                    # Move to defensive position if can't shoot effectively
                    if (agent_x, agent_y) != target_pos:
                        move_x, move_y = target_pos
                        command += f";MOVE {move_x} {move_y}"
                        print(f"DEBUG: Agent {agent['agent_id']} moving to defensive position ({move_x}, {move_y})", file=sys.stderr)
                    command += ";HUNKER_DOWN"
            else:
                # Target too far, move to defensive position
                if (agent_x, agent_y) != target_pos:
                    move_x, move_y = target_pos
                    command += f";MOVE {move_x} {move_y}"

                    # Check if we can shoot from new position
                    new_distance = manhattan_distance(move_x, move_y, target_enemy['x'], target_enemy['y'])
                    if new_distance <= 2 * init_agent['optimal_range']:
                        effective_wetness, _, _, _ = calculate_effective_wetness(
                            move_x, move_y, target_enemy, grid, width, height,
                            init_agent['soaking_power'], init_agent['optimal_range']
                        )

                        if effective_wetness > 0:
                            command += f";SHOOT {target_enemy['agent_id']}"
                            print(f"DEBUG: Agent {agent['agent_id']} moved and shooting, effective wetness: {effective_wetness}", file=sys.stderr)
                        else:
                            command += ";HUNKER_DOWN"
                    else:
                        command += ";HUNKER_DOWN"
                else:
                    command += ";HUNKER_DOWN"

        # Can't attack, focus on positioning
        else:
            if (agent_x, agent_y) != target_pos:
                move_x, move_y = target_pos
                command += f";MOVE {move_x} {move_y}"
                print(f"DEBUG: Agent {agent['agent_id']} moving to defensive position ({move_x}, {move_y})", file=sys.stderr)
            command += ";HUNKER_DOWN"

        # Add status message
        status = f"DC W:{agent['wetness']} A:{'+' if need_aggression else '-'}"
        command += f";MESSAGE {status}"

        agent_commands[agent['agent_id']] = command

    return agent_commands

def calculate_splash_threat_zones(enemy_agents, width, height):
    """Calculate all possible splash bomb threat zones from enemies with splash bombs."""
    threat_zones = {}  # Maps (x, y) -> list of enemies that can splash there

    for enemy in enemy_agents:
        if enemy.get('splash_bombs', 0) <= 0:
            continue

        enemy_threats = []

        # For each possible throw target within range 4
        for target_x in range(max(0, enemy['x'] - 4), min(width, enemy['x'] + 5)):
            for target_y in range(max(0, enemy['y'] - 4), min(height, enemy['y'] + 5)):
                throw_distance = manhattan_distance(enemy['x'], enemy['y'], target_x, target_y)
                if throw_distance > 4:
                    continue

                # Get splash area for this throw
                splash_area = get_splash_area(target_x, target_y, width, height)

                # Record this threat
                threat_info = {
                    'enemy': enemy,
                    'throw_target': (target_x, target_y),
                    'splash_area': splash_area
                }
                enemy_threats.append(threat_info)

                # Add each splash tile to threat zones
                for splash_x, splash_y in splash_area:
                    if (splash_x, splash_y) not in threat_zones:
                        threat_zones[(splash_x, splash_y)] = []
                    threat_zones[(splash_x, splash_y)].append(threat_info)

        print(f"DEBUG: Enemy {enemy['agent_id']} can create {len(enemy_threats)} splash threats", file=sys.stderr)

    return threat_zones

def check_position_safety(positions, threat_zones):
    """Check if proposed agent positions would allow multiple agents to be hit by same splash bomb."""
    violations = []

    # Group positions by the threats that could hit them
    threat_to_agents = {}

    for agent_id, pos in positions.items():
        pos_x, pos_y = pos

        # Check what threats could hit this position
        if (pos_x, pos_y) in threat_zones:
            for threat_info in threat_zones[(pos_x, pos_y)]:
                threat_key = (threat_info['enemy']['agent_id'], threat_info['throw_target'])

                if threat_key not in threat_to_agents:
                    threat_to_agents[threat_key] = []
                threat_to_agents[threat_key].append(agent_id)

    # Find violations (threats that could hit multiple agents)
    for threat_key, agents_at_risk in threat_to_agents.items():
        if len(agents_at_risk) > 1:
            enemy_id, throw_target = threat_key
            violations.append({
                'enemy_id': enemy_id,
                'throw_target': throw_target,
                'agents_at_risk': agents_at_risk,
                'risk_count': len(agents_at_risk)
            })

    return violations

def find_safe_positions_avoiding_splash(my_agents, target_enemy, enemy_agents, grid, width, height, agents_init):
    """Find positions where agents can attack while ensuring no splash bomb can hit multiple agents."""
    threat_zones = calculate_splash_threat_zones(enemy_agents, width, height)

    print(f"DEBUG: Calculated {len(threat_zones)} threat zone tiles", file=sys.stderr)

    # Find candidate positions for each agent
    agent_candidates = {}

    for agent in my_agents:
        agent_x, agent_y = agent['x'], agent['y']
        init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == agent['agent_id'])
        max_range = 2 * init_agent['optimal_range']

        candidates = []

        # Search for positions where we can shoot the target
        search_radius = min(max_range, 6)  # Limit search to reasonable area
        for dx in range(-search_radius, search_radius + 1):
            for dy in range(-search_radius, search_radius + 1):
                pos_x = agent_x + dx
                pos_y = agent_y + dy

                # Must be within map bounds
                if not (0 <= pos_x < width and 0 <= pos_y < height):
                    continue

                # Must not be on cover
                tile = next((t for t in grid if t['x'] == pos_x and t['y'] == pos_y), None)
                if tile and tile['tile_type'] > 0:
                    continue

                # Must be able to shoot target
                distance_to_target = manhattan_distance(pos_x, pos_y, target_enemy['x'], target_enemy['y'])
                if distance_to_target > max_range:
                    continue

                # Calculate position score
                # Range score
                if distance_to_target <= init_agent['optimal_range']:
                    range_score = 100
                else:
                    range_score = 50

                # Cover bonus
                cover_bonus = 0
                adjacent_tiles = get_adjacent_tiles(pos_x, pos_y, width, height)
                for adj_x, adj_y in adjacent_tiles:
                    adj_tile = next((t for t in grid if t['x'] == adj_x and t['y'] == adj_y), None)
                    if adj_tile and adj_tile['tile_type'] > 0:
                        cover_bonus = max(cover_bonus, adj_tile['tile_type'] * 15)

                # Distance from enemies (prefer further away)
                min_enemy_distance = float('inf')
                for enemy in enemy_agents:
                    enemy_distance = manhattan_distance(pos_x, pos_y, enemy['x'], enemy['y'])
                    min_enemy_distance = min(min_enemy_distance, enemy_distance)

                distance_safety_score = min(min_enemy_distance, 8) * 2

                total_score = range_score + cover_bonus + distance_safety_score

                candidates.append({
                    'pos': (pos_x, pos_y),
                    'score': total_score,
                    'distance_to_target': distance_to_target,
                    'cover_bonus': cover_bonus,
                    'distance_safety': distance_safety_score
                })

        # Sort candidates by score
        candidates.sort(key=lambda x: -x['score'])
        agent_candidates[agent['agent_id']] = candidates
        print(f"DEBUG: Agent {agent['agent_id']} has {len(candidates)} candidate positions", file=sys.stderr)

    # Find the best combination of positions that avoids splash bomb violations
    best_positions = {}

    # Try to assign positions iteratively, checking for violations
    agent_ids = sorted(my_agents, key=lambda x: x['agent_id'])

    for agent in agent_ids:
        agent_id = agent['agent_id']
        candidates = agent_candidates.get(agent_id, [])

        if not candidates:
            # No candidates, stay at current position
            best_positions[agent_id] = (agent['x'], agent['y'])
            continue

        # Try each candidate position
        position_assigned = False
        for candidate in candidates:
            # Test this position
            test_positions = best_positions.copy()
            test_positions[agent_id] = candidate['pos']

            # Check for violations
            violations = check_position_safety(test_positions, threat_zones)

            if not violations:
                # This position is safe
                best_positions[agent_id] = candidate['pos']
                position_assigned = True
                print(f"DEBUG: Agent {agent_id} assigned safe position {candidate['pos']}, score: {candidate['score']}", file=sys.stderr)
                break

        if not position_assigned:
            # All positions have violations, use current position as fallback
            best_positions[agent_id] = (agent['x'], agent['y'])
            print(f"DEBUG: Agent {agent_id} no safe position found, staying at current", file=sys.stderr)

    # Final safety check
    final_violations = check_position_safety(best_positions, threat_zones)
    if final_violations:
        print(f"DEBUG: WARNING - {len(final_violations)} splash violations remain in final positions", file=sys.stderr)
        for violation in final_violations:
            print(f"DEBUG: Violation - Enemy {violation['enemy_id']} can hit agents {violation['agents_at_risk']} at {violation['throw_target']}", file=sys.stderr)
    else:
        print(f"DEBUG: All positions are splash-safe", file=sys.stderr)

    # Convert to format expected by calling code
    final_positions = {}
    for agent in my_agents:
        agent_id = agent['agent_id']
        pos = best_positions.get(agent_id, (agent['x'], agent['y']))
        final_positions[agent_id] = {
            'pos': pos,
            'score': 100,  # Simplified since we prioritized safety
            'distance_to_target': manhattan_distance(pos[0], pos[1], target_enemy['x'], target_enemy['y']),
            'cover_bonus': 0,
            'splash_safety': 100 if not final_violations else 0,
            'distance_safety': 0
        }

    return final_positions, len(final_violations) > 0

def find_safe_shooting_positions(my_agents, target_enemy, enemy_agents, grid, width, height, agents_init):
    """Find positions where agents can shoot target while staying safe from splash bombs."""
    # Use the new comprehensive splash-safe position finding
    return find_safe_positions_avoiding_splash(my_agents, target_enemy, enemy_agents, grid, width, height, agents_init)

def execute_smart_focus_fire_strategy(my_agents, enemy_agents, grid, width, height, agents_init):
    """Execute smart focus fire - eliminate enemies one by one while staying safe from splash bombs."""
    agent_commands = {}

    print(f"DEBUG: Smart focus fire strategy with {len(my_agents)} agents", file=sys.stderr)

    if not enemy_agents:
        # No enemies, just hunker down
        for agent in my_agents:
            command = f"{agent['agent_id']};HUNKER_DOWN;MESSAGE No enemies"
            agent_commands[agent['agent_id']] = command
        return agent_commands

    # Find the nearest enemy to our group center
    center_x = sum(agent['x'] for agent in my_agents) / len(my_agents)
    center_y = sum(agent['y'] for agent in my_agents) / len(my_agents)

    # Prioritize enemies: nearest first, but prefer weak/exposed ones
    target_scores = []
    for enemy in enemy_agents:
        distance_to_center = manhattan_distance(center_x, center_y, enemy['x'], enemy['y'])
        weakness_score = enemy.get('wetness', 0)  # Prefer damaged enemies

        # Check how many of our agents can hit this enemy
        agents_in_range = 0
        for agent in my_agents:
            init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == agent['agent_id'])
            agent_distance = manhattan_distance(agent['x'], agent['y'], enemy['x'], enemy['y'])
            if agent_distance <= 2 * init_agent['optimal_range']:
                agents_in_range += 1

        # Score: prefer weak enemies we can hit with multiple agents, that are close
        score = weakness_score * 2 + agents_in_range * 10 - distance_to_center
        target_scores.append((enemy, score))

    target_scores.sort(key=lambda x: -x[1])
    target_enemy = target_scores[0][0]

    print(f"DEBUG: Target enemy {target_enemy['agent_id']} at ({target_enemy['x']}, {target_enemy['y']}), wetness: {target_enemy.get('wetness', 0)}", file=sys.stderr)

    # Find safe shooting positions
    shooting_positions, high_threat = find_safe_shooting_positions(my_agents, target_enemy, enemy_agents, grid, width, height, agents_init)

    # Process each agent
    for agent in sorted(my_agents, key=lambda x: x['agent_id']):
        command = f"{agent['agent_id']}"
        agent_x, agent_y = agent['x'], agent['y']
        init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == agent['agent_id'])

        # Get optimal shooting position
        position_info = shooting_positions.get(agent['agent_id'])
        target_pos = position_info['pos'] if position_info else (agent_x, agent_y)
        target_distance = position_info['distance_to_target'] if position_info else manhattan_distance(agent_x, agent_y, target_enemy['x'], target_enemy['y'])

        print(f"DEBUG: Agent {agent['agent_id']} at ({agent_x}, {agent_y}), target pos: {target_pos}, distance: {target_distance}", file=sys.stderr)

        # Check if agent is critically damaged
        if agent['wetness'] >= 70:
            # Survival mode - prioritize safety
            if (agent_x, agent_y) != target_pos:
                move_x, move_y = target_pos
                command += f";MOVE {move_x} {move_y}"
                print(f"DEBUG: Agent {agent['agent_id']} critical health, moving to safety ({move_x}, {move_y})", file=sys.stderr)
            command += ";HUNKER_DOWN"

        # Check if we can shoot from current position
        elif agent['cooldown'] == 0:
            current_distance = manhattan_distance(agent_x, agent_y, target_enemy['x'], target_enemy['y'])

            if current_distance <= 2 * init_agent['optimal_range']:
                effective_wetness, _, _, _ = calculate_effective_wetness(
                    agent_x, agent_y, target_enemy, grid, width, height,
                    init_agent['soaking_power'], init_agent['optimal_range']
                )

                if effective_wetness > 0:
                    command += f";SHOOT {target_enemy['agent_id']}"
                    print(f"DEBUG: Agent {agent['agent_id']} shooting from current position, effective wetness: {effective_wetness}", file=sys.stderr)
                else:
                    # Move to better position and try to shoot
                    if (agent_x, agent_y) != target_pos:
                        move_x, move_y = target_pos
                        command += f";MOVE {move_x} {move_y}"

                        # Try shooting from new position
                        new_distance = manhattan_distance(move_x, move_y, target_enemy['x'], target_enemy['y'])
                        if new_distance <= 2 * init_agent['optimal_range']:
                            new_effective_wetness, _, _, _ = calculate_effective_wetness(
                                move_x, move_y, target_enemy, grid, width, height,
                                init_agent['soaking_power'], init_agent['optimal_range']
                            )

                            if new_effective_wetness > 0:
                                command += f";SHOOT {target_enemy['agent_id']}"
                                print(f"DEBUG: Agent {agent['agent_id']} moved and shooting, effective wetness: {new_effective_wetness}", file=sys.stderr)
                            else:
                                command += ";HUNKER_DOWN"
                        else:
                            command += ";HUNKER_DOWN"
                    else:
                        command += ";HUNKER_DOWN"
            else:
                # Too far to shoot, move closer
                if (agent_x, agent_y) != target_pos:
                    move_x, move_y = target_pos
                    command += f";MOVE {move_x} {move_y}"

                    # Try shooting from new position
                    new_distance = manhattan_distance(move_x, move_y, target_enemy['x'], target_enemy['y'])
                    if new_distance <= 2 * init_agent['optimal_range']:
                        new_effective_wetness, _, _, _ = calculate_effective_wetness(
                            move_x, move_y, target_enemy, grid, width, height,
                            init_agent['soaking_power'], init_agent['optimal_range']
                        )

                        if new_effective_wetness > 0:
                            command += f";SHOOT {target_enemy['agent_id']}"
                            print(f"DEBUG: Agent {agent['agent_id']} moved closer and shooting, effective wetness: {new_effective_wetness}", file=sys.stderr)
                        else:
                            command += ";HUNKER_DOWN"
                    else:
                        command += ";HUNKER_DOWN"
                else:
                    command += ";HUNKER_DOWN"

        # On cooldown, just position
        else:
            if (agent_x, agent_y) != target_pos:
                move_x, move_y = target_pos
                command += f";MOVE {move_x} {move_y}"
                print(f"DEBUG: Agent {agent['agent_id']} on cooldown, moving to position ({move_x}, {move_y})", file=sys.stderr)
            command += ";HUNKER_DOWN"

        # Add status message
        threat_indicator = "!" if high_threat else ""
        status = f"SF{threat_indicator} W:{agent['wetness']} D:{target_distance}"
        command += f";MESSAGE {status}"

        agent_commands[agent['agent_id']] = command

    return agent_commands

def calculate_territory_control_value(x, y, my_agents, enemy_agents):
    """Calculate how much this position helps with territory control."""
    control_value = 0

    # Distance to center of map (prefer central positions)
    center_distance = abs(x - 8) + abs(y - 6)  # Assuming roughly 16x12 map
    control_value += max(0, 20 - center_distance)

    # Distance to nearest enemy (prefer positions that threaten enemies)
    if enemy_agents:
        min_enemy_distance = min(manhattan_distance(x, y, e['x'], e['y']) for e in enemy_agents)
        control_value += max(0, 15 - min_enemy_distance)

    # Distance to nearest friendly (avoid clustering too much)
    if my_agents:
        min_friendly_distance = min(manhattan_distance(x, y, a['x'], a['y']) for a in my_agents)
        if min_friendly_distance < 3:
            control_value -= 10  # Penalty for clustering

    return control_value

def find_best_position(agent, grid, width, height, my_agents, enemy_agents, prioritize_cover=True):
    """Find the best position considering both cover and territory control."""
    current_x, current_y = agent['x'], agent['y']
    adjacent_tiles = get_adjacent_tiles(current_x, current_y, width, height)

    best_score = -1000
    best_pos = None
    best_cover = 0

    for x, y in adjacent_tiles:
        # Skip if the tile is a cover tile (impassable)
        tile = next((t for t in grid if t['x'] == x and t['y'] == y), None)
        if tile and tile['tile_type'] > 0:
            continue  # Cannot move onto cover tiles

        # Check for other agents on this tile
        occupied = any(a['x'] == x and a['y'] == y for a in my_agents + enemy_agents if a != agent)
        if occupied:
            continue

        # Calculate cover value
        cover_adjacent = get_adjacent_tiles(x, y, width, height)
        max_cover = 0
        for cx, cy in cover_adjacent:
            cover_tile = next((t for t in grid if t['x'] == cx and t['y'] == cy), None)
            if cover_tile and cover_tile['tile_type'] > max_cover:
                max_cover = cover_tile['tile_type']

        # Calculate territory control value
        territory_value = calculate_territory_control_value(x, y, my_agents, enemy_agents)

        # Combined score
        if prioritize_cover:
            score = max_cover * 100 + territory_value
        else:
            score = territory_value + max_cover * 20

        if score > best_score:
            best_score = score
            best_pos = (x, y)
            best_cover = max_cover

    return best_pos, best_cover

def find_closest_cover_position(agent, grid, width, height):
    """Find the closest tile adjacent to the highest cover (one move away)."""
    current_x, current_y = agent['x'], agent['y']
    adjacent_tiles = get_adjacent_tiles(current_x, current_y, width, height)
    best_cover = 0
    best_pos = None
    for x, y in adjacent_tiles:
        # Skip if the tile is a cover tile (impassable)
        tile = next((t for t in grid if t['x'] == x and t['y'] == y), None)
        if tile and tile['tile_type'] > 0:
            continue  # Cannot move onto cover tiles
        # Check adjacent tiles for cover
        cover_adjacent = get_adjacent_tiles(x, y, width, height)
        max_cover = 0
        for cx, cy in cover_adjacent:
            cover_tile = next((t for t in grid if t['x'] == cx and t['y'] == cy), None)
            if cover_tile and cover_tile['tile_type'] > max_cover:
                max_cover = cover_tile['tile_type']
        if max_cover > best_cover:
            best_cover = max_cover
            best_pos = (x, y)
        elif max_cover == best_cover and best_pos is None:
            best_pos = (x, y)  # Default to first valid position if tied
    return best_pos, best_cover

def get_effective_cover_value(shooter_x, shooter_y, enemy, grid, width, height):
    """Calculate effective cover for an enemy from the shooter's perspective."""
    enemy_x, enemy_y = enemy['x'], enemy['y']
    adjacent = get_adjacent_tiles(enemy_x, enemy_y, width, height)
    max_cover = 0
    cover_positions = []
    
    # If shooter and enemy are adjacent to the same cover, cover is ignored
    shooter_adjacent = get_adjacent_tiles(shooter_x, shooter_y, width, height)
    for x, y in adjacent:
        tile = next((t for t in grid if t['x'] == x and t['y'] == y), None)
        if tile and tile['tile_type'] > 0:
            if (x, y) in shooter_adjacent:
                continue  # Cover ignored if both are adjacent
            # Check if the cover tile lies directly on the shot path
            dx = enemy_x - shooter_x
            dy = enemy_y - shooter_y
            is_between = False
            if dx != 0 or dy != 0:
                if abs(dx) >= abs(dy):  # Primarily horizontal shot
                    if (dx > 0 and x < enemy_x and x >= shooter_x and y == enemy_y) or \
                       (dx < 0 and x > enemy_x and x <= shooter_x and y == enemy_y):
                        is_between = True
                else:  # Primarily vertical shot
                    if (dy > 0 and y < enemy_y and y >= shooter_y and x == enemy_x) or \
                       (dy < 0 and y > enemy_y and y <= shooter_y and x == enemy_x):
                        is_between = True
            if is_between:
                max_cover = max(max_cover, tile['tile_type'])
                cover_positions.append((x, y, tile['tile_type']))
    
    return max_cover, cover_positions

def calculate_effective_wetness(shooter_x, shooter_y, enemy, grid, width, height, soaking_power, optimal_range):
    """Calculate the effective wetness applied to an enemy."""
    distance = manhattan_distance(shooter_x, shooter_y, enemy['x'], enemy['y'])
    cover_value, cover_positions = get_effective_cover_value(shooter_x, shooter_y, enemy, grid, width, height)
    
    # Cover multiplier
    cover_multiplier = 1.0
    if cover_value == 1:
        cover_multiplier = 0.5  # Low cover: 50% damage
    elif cover_value == 2:
        cover_multiplier = 0.25  # High cover: 25% damage
    
    # Distance multiplier
    if distance > 2 * optimal_range:
        distance_multiplier = 0.0  # Shots beyond 2 * optimal_range fail
    elif distance > optimal_range:
        distance_multiplier = 0.5  # Reduced damage beyond optimal_range
    else:
        distance_multiplier = 1.0  # Full damage within optimal_range
    
    # Effective wetness
    effective_wetness = soaking_power * cover_multiplier * distance_multiplier
    return effective_wetness, cover_value, distance, cover_positions

def find_best_target_enemy(shooter_x, shooter_y, enemies, grid, width, height, optimal_range, soaking_power, taken_enemies):
    """Rank enemies by effective wetness and filter by distance."""
    if not enemies:
        return None
    # Rank all enemies by effective wetness
    enemy_data = []
    for enemy in enemies:
        if enemy['agent_id'] in taken_enemies:
            continue
        wetness, cover_value, distance, cover_pos = calculate_effective_wetness(
            shooter_x, shooter_y, enemy, grid, width, height, soaking_power, optimal_range
        )
        # Filter by distance: within (0.5 * map_width) + 1
        max_distance = (width * 0.5) + 1
        if distance <= max_distance:
            enemy_data.append((enemy, wetness, cover_value, distance, cover_pos))
    
    if not enemy_data:
        return None
    
    # Sort by effective wetness (descending), then distance (ascending)
    enemy_data.sort(key=lambda x: (-x[1], x[3]))
    print(f"DEBUG: Agent at ({shooter_x}, {shooter_y}) enemy options: {[(e[0]['agent_id'], e[1], e[2], e[3], e[4]) for e in enemy_data]}", file=sys.stderr)
    return enemy_data[0][0] if enemy_data else None

# Read initialization input
my_id = int(input())
agent_data_count = int(input())
agents_init = []
for _ in range(agent_data_count):
    agent_id, player, shoot_cooldown, optimal_range, soaking_power, splash_bombs = map(int, input().split())
    agents_init.append({
        'agent_id': agent_id,
        'player': player,
        'shoot_cooldown': shoot_cooldown,
        'optimal_range': optimal_range,
        'soaking_power': soaking_power,
        'splash_bombs': splash_bombs
    })
width, height = map(int, input().split())
grid = []
for i in range(height):
    inputs = input().split()
    for j in range(width):
        x = int(inputs[3*j])
        y = int(inputs[3*j+1])
        tile_type = int(inputs[3*j+2])
        grid.append({'x': x, 'y': y, 'tile_type': tile_type})

# Main game loop
while True:
    try:
        agent_count = int(input())
        agents = []
        for _ in range(agent_count):
            agent_id, x, y, cooldown, splash_bombs, wetness = map(int, input().split())
            agents.append({
                'agent_id': agent_id,
                'x': x,
                'y': y,
                'cooldown': cooldown,
                'splash_bombs': splash_bombs,
                'wetness': wetness
            })
        my_agent_count = int(input())

        # Find my agents and enemy agents
        my_agents = [agent for agent in agents if any(init_agent['agent_id'] == agent['agent_id'] and init_agent['player'] == my_id for init_agent in agents_init)]
        enemy_agents = [agent for agent in agents if any(init_agent['agent_id'] == agent['agent_id'] and init_agent['player'] != my_id for init_agent in agents_init)]

        # Debug: Print current state
        print(f"DEBUG: Turn - My agents: {[(a['agent_id'], a['x'], a['y'], a['splash_bombs'], a['wetness']) for a in my_agents]}", file=sys.stderr)
        print(f"DEBUG: Turn - Enemy agents: {[(a['agent_id'], a['x'], a['y'], a['wetness']) for a in enemy_agents]}", file=sys.stderr)
        print(f"DEBUG: Full game mode - territory control and elimination", file=sys.stderr)

        # Strategy selection - change this to switch strategies
        ACTIVE_STRATEGY = "smart_focus_fire"  # Options: "balanced", "coordinated_focus_fire", "defensive_control", "smart_focus_fire"

        if ACTIVE_STRATEGY == "balanced":
            agent_commands = execute_balanced_strategy(my_agents, enemy_agents, grid, width, height, agents_init)
        elif ACTIVE_STRATEGY == "coordinated_focus_fire":
            agent_commands = execute_coordinated_focus_fire_strategy(my_agents, enemy_agents, grid, width, height, agents_init)
        elif ACTIVE_STRATEGY == "defensive_control":
            agent_commands = execute_defensive_control_strategy(my_agents, enemy_agents, grid, width, height, agents_init)
        elif ACTIVE_STRATEGY == "smart_focus_fire":
            agent_commands = execute_smart_focus_fire_strategy(my_agents, enemy_agents, grid, width, height, agents_init)
        else:
            # Fallback to balanced strategy
            agent_commands = execute_balanced_strategy(my_agents, enemy_agents, grid, width, height, agents_init)

        # Output commands in agent ID order to ensure consistent output
        for my_agent in sorted(my_agents, key=lambda x: x['agent_id']):
            print(agent_commands[my_agent['agent_id']])

    except EOFError:
        # Game ended
        break
    except Exception as e:
        print(f"ERROR: {e}", file=sys.stderr)
        break