import sys
from collections import deque

def manhattan_distance(x1, y1, x2, y2):
    return abs(x1 - x2) + abs(y1 - y2)

def get_adjacent_tiles(x, y, width, height):
    """Return list of orthogonally adjacent tile coordinates."""
    directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]  # Down, Up, Right, Left
    adjacent = []
    for dx, dy in directions:
        nx, ny = x + dx, y + dy
        if 0 <= nx < width and 0 <= ny < height:
            adjacent.append((nx, ny))
    return adjacent

def get_splash_area(x, y, width, height):
    """Return list of all tiles affected by splash bomb (target + 8 surrounding tiles)."""
    splash_tiles = [(x, y)]  # Target tile
    # Add all 8 surrounding tiles (orthogonal + diagonal)
    directions = [(0, 1), (0, -1), (1, 0), (-1, 0), (1, 1), (1, -1), (-1, 1), (-1, -1)]
    for dx, dy in directions:
        nx, ny = x + dx, y + dy
        if 0 <= nx < width and 0 <= ny < height:
            splash_tiles.append((nx, ny))
    return splash_tiles

def can_move_to_tile(x, y, grid, agents):
    """Check if a tile is passable (no cover, no other agents)."""
    # Check for cover
    tile = next((t for t in grid if t['x'] == x and t['y'] == y), None)
    if tile and tile['tile_type'] > 0:
        return False
    # Check for other agents
    for agent in agents:
        if agent['x'] == x and agent['y'] == y:
            return False
    return True

def find_reachable_tiles(start_x, start_y, grid, agents, width, height, max_distance=None):
    """Find all tiles reachable from start position using BFS."""
    visited = set()
    reachable = {}  # (x, y) -> distance
    queue = deque([(start_x, start_y, 0)])
    visited.add((start_x, start_y))
    reachable[(start_x, start_y)] = 0

    while queue:
        x, y, dist = queue.popleft()

        if max_distance and dist >= max_distance:
            continue

        for nx, ny in get_adjacent_tiles(x, y, width, height):
            if (nx, ny) not in visited and can_move_to_tile(nx, ny, grid, agents):
                visited.add((nx, ny))
                reachable[(nx, ny)] = dist + 1
                queue.append((nx, ny, dist + 1))

    return reachable

def is_safe_splash_target(target_x, target_y, my_agents, width, height):
    """Check if throwing a splash bomb at target won't hit any of our agents."""
    splash_area = get_splash_area(target_x, target_y, width, height)
    for agent in my_agents:
        if (agent['x'], agent['y']) in splash_area:
            return False
    return True

def find_best_splash_targets(agent_x, agent_y, enemy_agents, my_agents, width, height):
    """Find the best splash bomb targets that maximize enemy damage while avoiding friendly fire."""
    max_throw_range = 4
    best_targets = []

    print(f"DEBUG: Finding targets for agent at ({agent_x}, {agent_y})", file=sys.stderr)

    # Consider all possible throw targets within range
    for target_x in range(max(0, agent_x - max_throw_range), min(width, agent_x + max_throw_range + 1)):
        for target_y in range(max(0, agent_y - max_throw_range), min(height, agent_y + max_throw_range + 1)):
            distance = manhattan_distance(agent_x, agent_y, target_x, target_y)
            if distance > max_throw_range:
                continue

            # Check if this target is safe (won't hit our agents)
            if not is_safe_splash_target(target_x, target_y, my_agents, width, height):
                continue

            # Count how many enemies would be hit
            splash_area = get_splash_area(target_x, target_y, width, height)
            enemies_hit = []
            total_enemy_wetness = 0
            for enemy in enemy_agents:
                if (enemy['x'], enemy['y']) in splash_area:
                    enemies_hit.append(enemy)
                    # Consider current wetness - prioritize enemies closer to elimination
                    total_enemy_wetness += enemy.get('wetness', 0)

            if enemies_hit:
                # Calculate total damage potential (30 wetness per enemy hit)
                total_damage = len(enemies_hit) * 30
                # Bonus for hitting enemies that are already damaged
                elimination_potential = sum(1 for enemy in enemies_hit if enemy.get('wetness', 0) + 30 >= 100)

                best_targets.append({
                    'target_pos': (target_x, target_y),
                    'distance': distance,
                    'enemies_hit': enemies_hit,
                    'total_damage': total_damage,
                    'enemy_count': len(enemies_hit),
                    'elimination_potential': elimination_potential,
                    'total_enemy_wetness': total_enemy_wetness
                })

    # Sort by: elimination potential, most enemies hit, total enemy wetness, then closest distance
    best_targets.sort(key=lambda x: (-x['elimination_potential'], -x['enemy_count'], -x['total_enemy_wetness'], x['distance']))
    return best_targets

def find_optimal_throw_position(agent, enemy_agents, my_agents, grid, width, height):
    """Find the best position for the agent to move to for optimal splash bomb throwing."""
    # Create a copy of agents list excluding the current agent for pathfinding
    other_agents = [a for a in my_agents if a['agent_id'] != agent['agent_id']]
    reachable = find_reachable_tiles(agent['x'], agent['y'], grid, other_agents, width, height, max_distance=3)

    best_position = None
    best_targets = []
    best_score = 0

    # Try each reachable position
    for (pos_x, pos_y), move_distance in reachable.items():
        # Find best splash targets from this position
        targets = find_best_splash_targets(pos_x, pos_y, enemy_agents, my_agents, width, height)

        if targets:
            # Score based on best target from this position
            best_target = targets[0]
            # Prefer positions that can hit more enemies, with elimination potential, shorter move distance
            score = (best_target['elimination_potential'] * 10000 +
                    best_target['enemy_count'] * 1000 +
                    best_target['total_damage'] - move_distance * 10)

            if score > best_score:
                best_score = score
                best_position = (pos_x, pos_y)
                best_targets = targets

    return best_position, best_targets

def find_closest_cover_position(agent, grid, width, height):
    """Find the closest tile adjacent to the highest cover (one move away)."""
    current_x, current_y = agent['x'], agent['y']
    adjacent_tiles = get_adjacent_tiles(current_x, current_y, width, height)
    best_cover = 0
    best_pos = None
    for x, y in adjacent_tiles:
        # Skip if the tile is a cover tile (impassable)
        tile = next((t for t in grid if t['x'] == x and t['y'] == y), None)
        if tile and tile['tile_type'] > 0:
            continue  # Cannot move onto cover tiles
        # Check adjacent tiles for cover
        cover_adjacent = get_adjacent_tiles(x, y, width, height)
        max_cover = 0
        for cx, cy in cover_adjacent:
            cover_tile = next((t for t in grid if t['x'] == cx and t['y'] == cy), None)
            if cover_tile and cover_tile['tile_type'] > max_cover:
                max_cover = cover_tile['tile_type']
        if max_cover > best_cover:
            best_cover = max_cover
            best_pos = (x, y)
        elif max_cover == best_cover and best_pos is None:
            best_pos = (x, y)  # Default to first valid position if tied
    return best_pos, best_cover

def get_effective_cover_value(shooter_x, shooter_y, enemy, grid, width, height):
    """Calculate effective cover for an enemy from the shooter's perspective."""
    enemy_x, enemy_y = enemy['x'], enemy['y']
    adjacent = get_adjacent_tiles(enemy_x, enemy_y, width, height)
    max_cover = 0
    cover_positions = []
    
    # If shooter and enemy are adjacent to the same cover, cover is ignored
    shooter_adjacent = get_adjacent_tiles(shooter_x, shooter_y, width, height)
    for x, y in adjacent:
        tile = next((t for t in grid if t['x'] == x and t['y'] == y), None)
        if tile and tile['tile_type'] > 0:
            if (x, y) in shooter_adjacent:
                continue  # Cover ignored if both are adjacent
            # Check if the cover tile lies directly on the shot path
            dx = enemy_x - shooter_x
            dy = enemy_y - shooter_y
            is_between = False
            if dx != 0 or dy != 0:
                if abs(dx) >= abs(dy):  # Primarily horizontal shot
                    if (dx > 0 and x < enemy_x and x >= shooter_x and y == enemy_y) or \
                       (dx < 0 and x > enemy_x and x <= shooter_x and y == enemy_y):
                        is_between = True
                else:  # Primarily vertical shot
                    if (dy > 0 and y < enemy_y and y >= shooter_y and x == enemy_x) or \
                       (dy < 0 and y > enemy_y and y <= shooter_y and x == enemy_x):
                        is_between = True
            if is_between:
                max_cover = max(max_cover, tile['tile_type'])
                cover_positions.append((x, y, tile['tile_type']))
    
    return max_cover, cover_positions

def calculate_effective_wetness(shooter_x, shooter_y, enemy, grid, width, height, soaking_power, optimal_range):
    """Calculate the effective wetness applied to an enemy."""
    distance = manhattan_distance(shooter_x, shooter_y, enemy['x'], enemy['y'])
    cover_value, cover_positions = get_effective_cover_value(shooter_x, shooter_y, enemy, grid, width, height)
    
    # Cover multiplier
    cover_multiplier = 1.0
    if cover_value == 1:
        cover_multiplier = 0.5  # Low cover: 50% damage
    elif cover_value == 2:
        cover_multiplier = 0.25  # High cover: 25% damage
    
    # Distance multiplier
    if distance > 2 * optimal_range:
        distance_multiplier = 0.0  # Shots beyond 2 * optimal_range fail
    elif distance > optimal_range:
        distance_multiplier = 0.5  # Reduced damage beyond optimal_range
    else:
        distance_multiplier = 1.0  # Full damage within optimal_range
    
    # Effective wetness
    effective_wetness = soaking_power * cover_multiplier * distance_multiplier
    return effective_wetness, cover_value, distance, cover_positions

def find_best_target_enemy(shooter_x, shooter_y, enemies, grid, width, height, optimal_range, soaking_power, taken_enemies):
    """Rank enemies by effective wetness and filter by distance."""
    if not enemies:
        return None
    # Rank all enemies by effective wetness
    enemy_data = []
    for enemy in enemies:
        if enemy['agent_id'] in taken_enemies:
            continue
        wetness, cover_value, distance, cover_pos = calculate_effective_wetness(
            shooter_x, shooter_y, enemy, grid, width, height, soaking_power, optimal_range
        )
        # Filter by distance: within (0.5 * map_width) + 1
        max_distance = (width * 0.5) + 1
        if distance <= max_distance:
            enemy_data.append((enemy, wetness, cover_value, distance, cover_pos))
    
    if not enemy_data:
        return None
    
    # Sort by effective wetness (descending), then distance (ascending)
    enemy_data.sort(key=lambda x: (-x[1], x[3]))
    print(f"DEBUG: Agent at ({shooter_x}, {shooter_y}) enemy options: {[(e[0]['agent_id'], e[1], e[2], e[3], e[4]) for e in enemy_data]}", file=sys.stderr)
    return enemy_data[0][0] if enemy_data else None

# Read initialization input
my_id = int(input())
agent_data_count = int(input())
agents_init = []
for _ in range(agent_data_count):
    agent_id, player, shoot_cooldown, optimal_range, soaking_power, splash_bombs = map(int, input().split())
    agents_init.append({
        'agent_id': agent_id,
        'player': player,
        'shoot_cooldown': shoot_cooldown,
        'optimal_range': optimal_range,
        'soaking_power': soaking_power,
        'splash_bombs': splash_bombs
    })
width, height = map(int, input().split())
grid = []
for i in range(height):
    inputs = input().split()
    for j in range(width):
        x = int(inputs[3*j])
        y = int(inputs[3*j+1])
        tile_type = int(inputs[3*j+2])
        grid.append({'x': x, 'y': y, 'tile_type': tile_type})

# Single-turn game loop
agent_count = int(input())
agents = []
for _ in range(agent_count):
    agent_id, x, y, cooldown, splash_bombs, wetness = map(int, input().split())
    agents.append({
        'agent_id': agent_id,
        'x': x,
        'y': y,
        'cooldown': cooldown,
        'splash_bombs': splash_bombs,
        'wetness': wetness
    })
my_agent_count = int(input())

# Find my agents and enemy agents
my_agents = [agent for agent in agents if any(init_agent['agent_id'] == agent['agent_id'] and init_agent['player'] == my_id for init_agent in agents_init)]
enemy_agents = [agent for agent in agents if any(init_agent['agent_id'] == agent['agent_id'] and init_agent['player'] != my_id for init_agent in agents_init)]

# Debug: Print initial state
print(f"DEBUG: My agents: {[(a['agent_id'], a['x'], a['y'], a['splash_bombs']) for a in my_agents]}", file=sys.stderr)
print(f"DEBUG: Enemy agents: {[(a['agent_id'], a['x'], a['y'], a['wetness']) for a in enemy_agents]}", file=sys.stderr)
print(f"DEBUG: Grid cover tiles: {[t for t in grid if t['tile_type'] > 0]}", file=sys.stderr)
print(f"DEBUG: Training mode - using THROW instead of SHOOT", file=sys.stderr)

# Training mode: Use THROW command instead of SHOOT
# Track taken enemy groups to ensure unique targets
taken_enemy_groups = set()

# Generate commands for each of my agents
for my_agent in sorted(my_agents, key=lambda x: x['agent_id']):
    command = f"{my_agent['agent_id']}"

    print(f"DEBUG: Agent {my_agent['agent_id']} has {my_agent['splash_bombs']} splash bombs", file=sys.stderr)

    # Check if agent has splash bombs available
    if my_agent['splash_bombs'] > 0:
        # First, check if we can throw from current position
        current_throw_targets = find_best_splash_targets(my_agent['x'], my_agent['y'], enemy_agents, my_agents, width, height)

        # Filter out targets that hit enemies already targeted by other agents
        available_current_targets = []
        for target in current_throw_targets:
            enemy_ids = {enemy['agent_id'] for enemy in target['enemies_hit']}
            if not enemy_ids.intersection(taken_enemy_groups):
                available_current_targets.append(target)

        if available_current_targets:
            # We can throw from current position - do it!
            best_target = available_current_targets[0]
            target_x, target_y = best_target['target_pos']

            # Double-check the distance to make sure it's valid
            throw_distance = manhattan_distance(my_agent['x'], my_agent['y'], target_x, target_y)
            print(f"DEBUG: Agent {my_agent['agent_id']} considering throw from ({my_agent['x']}, {my_agent['y']}) to ({target_x}, {target_y}), distance: {throw_distance}", file=sys.stderr)

            # CONSERVATIVE APPROACH: Only throw if distance is strictly <= 4
            if throw_distance <= 4:
                command += f";THROW {target_x} {target_y}"

                # Mark these enemies as taken
                for enemy in best_target['enemies_hit']:
                    taken_enemy_groups.add(enemy['agent_id'])

                print(f"DEBUG: Agent {my_agent['agent_id']} THROWING from ({my_agent['x']}, {my_agent['y']}) to ({target_x}, {target_y}), distance: {throw_distance}, hitting {best_target['enemy_count']} enemies: {[e['agent_id'] for e in best_target['enemies_hit']]}", file=sys.stderr)
            else:
                print(f"DEBUG: Agent {my_agent['agent_id']} target too far ({throw_distance} > 4), moving closer instead", file=sys.stderr)
                # Move closer to enemies
                optimal_pos, _ = find_optimal_throw_position(my_agent, enemy_agents, my_agents, grid, width, height)
                if optimal_pos and (my_agent['x'], my_agent['y']) != optimal_pos:
                    move_x, move_y = optimal_pos
                    command += f";MOVE {move_x} {move_y}"
                    print(f"DEBUG: Agent {my_agent['agent_id']} moving to ({move_x}, {move_y}) to get closer", file=sys.stderr)
                command += ";HUNKER_DOWN"
        else:
            # No good targets from current position, move to better position
            print(f"DEBUG: Agent {my_agent['agent_id']} no targets from current position, finding better position", file=sys.stderr)
            optimal_pos, best_targets = find_optimal_throw_position(my_agent, enemy_agents, my_agents, grid, width, height)

            if optimal_pos and (my_agent['x'], my_agent['y']) != optimal_pos:
                move_x, move_y = optimal_pos
                command += f";MOVE {move_x} {move_y}"
                print(f"DEBUG: Agent {my_agent['agent_id']} moving to optimal position ({move_x}, {move_y}) for future throws", file=sys.stderr)

            # Don't throw this turn, just position for next turn
            command += ";HUNKER_DOWN"
    else:
        # No splash bombs left, find cover position and try to stay alive
        best_pos, best_cover = find_closest_cover_position(my_agent, grid, width, height)
        print(f"DEBUG: Agent {my_agent['agent_id']} no bombs left, seeking cover: {best_pos}, cover value: {best_cover}", file=sys.stderr)

        if best_pos and (my_agent['x'], my_agent['y']) != best_pos:
            move_x, move_y = best_pos
            command += f";MOVE {move_x} {move_y}"

        # Try to use SHOOT command as fallback (even though it's disabled in training)
        # This keeps the agent ready for when SHOOT is re-enabled
        init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == my_agent['agent_id'])
        if my_agent['cooldown'] == 0 and enemy_agents:
            # Find closest enemy
            closest_enemy = min(enemy_agents,
                              key=lambda e: manhattan_distance(my_agent['x'], my_agent['y'], e['x'], e['y']))
            distance = manhattan_distance(my_agent['x'], my_agent['y'], closest_enemy['x'], closest_enemy['y'])
            if distance <= 2 * init_agent['optimal_range']:
                command += f";SHOOT {closest_enemy['agent_id']}"
            else:
                command += ";HUNKER_DOWN"
        else:
            command += ";HUNKER_DOWN"

    # Add a debug message
    bombs_left = my_agent['splash_bombs']
    command += f";MESSAGE Bombs:{bombs_left}"

    print(command)