# Agent Strategy System

## Overview
The agent now has a modular strategy system that allows easy switching between different approaches. Change the `ACTIVE_STRATEGY` variable in the main game loop to test different strategies.

## Available Strategies

### 1. "balanced" - Balanced Territory Control Strategy
**Description**: The original full-game strategy focusing on balanced play
**Key Features**:
- Adaptive positioning (cover vs territory based on health)
- Multi-layered combat (splash bombs → shooting → positioning)
- Individual agent decision making
- Territory control considerations

**Best For**: General gameplay, mixed scenarios

### 2. "coordinated_focus_fire" - Coordinated Focus Fire Strategy
**Description**: All agents work together to eliminate enemies one at a time
**Key Features**:
- **Formation Fighting**: Agents form a line 2 spaces apart facing the target
- **Focus Fire**: All agents target the closest enemy simultaneously
- **Coordinated Positioning**: Agents move to optimal formation positions
- **Range Management**: Positions slightly outside optimal range (distance 4) for safety
- **Survival Priority**: Critically damaged agents (wetness ≥ 70) seek cover instead

**Tactical Approach**:
1. **Target Selection**: Always focus on the closest enemy to the group center
2. **Formation**: Create a line formation perpendicular to the approach direction
3. **Positioning**: Stay ~4 tiles from target (just outside typical optimal range)
4. **Attack Priority**: Shooting preferred over splash bombs for coordination
5. **Safety**: Agents with high wetness retreat to cover

**Best For**: Scenarios where focused elimination is more valuable than territory control

### 3. "defensive_control" - Defensive Territory Control Strategy
**Description**: Map control focused strategy with spacing and selective engagement
**Key Features**:
- Voronoi-based territory analysis
- 3+ tile spacing to prevent splash bomb casualties
- Adaptive aggression based on territory advantage
- Cover prioritization and safety positioning

**Best For**: Scenarios where map control and survival are more important than quick elimination

### 4. "smart_focus_fire" - Smart Focus Fire Strategy (CURRENT DEFAULT)
**Description**: Coordinated elimination with intelligent splash bomb threat assessment
**Key Features**:
- **Splash Threat Detection**: Automatically detects when enemies can hit multiple agents
- **Adaptive Spacing**: Clusters when safe, spreads out when threatened
- **Target Prioritization**: Focuses on weak/exposed enemies that multiple agents can hit
- **Position Optimization**: Finds positions with cover, safety, and optimal range
- **Conflict Resolution**: Prevents agents from trying to occupy same tile
- **Move+Shoot Efficiency**: Moves to optimal position and shoots in same turn

**Tactical Approach**:
1. **Threat Assessment**: Scans for splash bomb threats that could hit multiple agents
2. **Target Selection**: Prioritizes weak enemies that multiple agents can engage
3. **Position Finding**: Calculates optimal shooting positions considering cover and safety
4. **Coordinated Elimination**: All agents focus fire on same target for quick kills
5. **Safety First**: Spreads out when splash threats detected, clusters when safe

**Best For**: Balanced approach that adapts to threats while maintaining aggressive focus fire

## Strategy Selection

To change strategy, modify this line in the main game loop:
```python
ACTIVE_STRATEGY = "smart_focus_fire"  # Options: "balanced", "coordinated_focus_fire", "defensive_control", "smart_focus_fire"
```

## Implementation Notes

### Modular Design
- Each strategy is implemented as a separate function
- Easy to add new strategies without affecting existing ones
- Shared utility functions for common operations

### Formation Logic (Focus Fire)
- Calculates optimal approach direction to target
- Forms perpendicular line for maximum firepower
- Maintains 2-space separation between agents
- Handles map boundaries and cover obstacles

### Safety Features
- Agents retreat when critically damaged
- Formation positions avoid cover tiles
- Range management keeps agents safer from retaliation

## Future Strategy Ideas
1. **"aggressive_rush"** - Close-range coordinated assault
2. **"defensive_control"** - Heavy cover usage and territory denial
3. **"splash_specialist"** - Maximize splash bomb effectiveness
4. **"adaptive_hybrid"** - Switch tactics based on game state

## Testing Results
- **Balanced Strategy**: Good general performance, individual flexibility
- **Coordinated Focus Fire**: Strong elimination potential, but vulnerable to splash bombs
- **Defensive Control**: Good survival but too passive, poor arena performance
- **Smart Focus Fire**: Best balance of aggression and safety, adapts to threats

### Smart Focus Fire Performance
- **Threat Detection**: Successfully identifies splash bomb threats (5 detected in test)
- **Adaptive Behavior**: Switches between clustering (safe) and spreading (threatened)
- **Focus Fire Efficiency**: 25+ damage per turn when coordinated
- **Position Optimization**: Finds optimal shooting positions with cover and safety
- **Conflict Resolution**: Prevents agent collisions while maintaining coordination

The modular system makes it easy to experiment with different approaches and find the optimal strategy for various game scenarios. Smart Focus Fire is currently the default as it provides the best balance of aggressive elimination and defensive positioning.
