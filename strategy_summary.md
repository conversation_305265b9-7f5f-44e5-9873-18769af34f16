# Agent Strategy System

## Overview
The agent now has a modular strategy system that allows easy switching between different approaches. Change the `ACTIVE_STRATEGY` variable in the main game loop to test different strategies.

## Available Strategies

### 1. "balanced" - Balanced Territory Control Strategy
**Description**: The original full-game strategy focusing on balanced play
**Key Features**:
- Adaptive positioning (cover vs territory based on health)
- Multi-layered combat (splash bombs → shooting → positioning)
- Individual agent decision making
- Territory control considerations

**Best For**: General gameplay, mixed scenarios

### 2. "coordinated_focus_fire" - Coordinated Focus Fire Strategy
**Description**: All agents work together to eliminate enemies one at a time
**Key Features**:
- **Formation Fighting**: Agents form a line 2 spaces apart facing the target
- **Focus Fire**: All agents target the closest enemy simultaneously
- **Coordinated Positioning**: Agents move to optimal formation positions
- **Range Management**: Positions slightly outside optimal range (distance 4) for safety
- **Survival Priority**: Critically damaged agents (wetness ≥ 70) seek cover instead

**Tactical Approach**:
1. **Target Selection**: Always focus on the closest enemy to the group center
2. **Formation**: Create a line formation perpendicular to the approach direction
3. **Positioning**: Stay ~4 tiles from target (just outside typical optimal range)
4. **Attack Priority**: Shooting preferred over splash bombs for coordination
5. **Safety**: Agents with high wetness retreat to cover

**Best For**: Scenarios where focused elimination is more valuable than territory control

## Strategy Selection

To change strategy, modify this line in the main game loop:
```python
ACTIVE_STRATEGY = "coordinated_focus_fire"  # Options: "balanced", "coordinated_focus_fire"
```

## Implementation Notes

### Modular Design
- Each strategy is implemented as a separate function
- Easy to add new strategies without affecting existing ones
- Shared utility functions for common operations

### Formation Logic (Focus Fire)
- Calculates optimal approach direction to target
- Forms perpendicular line for maximum firepower
- Maintains 2-space separation between agents
- Handles map boundaries and cover obstacles

### Safety Features
- Agents retreat when critically damaged
- Formation positions avoid cover tiles
- Range management keeps agents safer from retaliation

## Future Strategy Ideas
1. **"aggressive_rush"** - Close-range coordinated assault
2. **"defensive_control"** - Heavy cover usage and territory denial
3. **"splash_specialist"** - Maximize splash bomb effectiveness
4. **"adaptive_hybrid"** - Switch tactics based on game state

## Testing Results
- **Balanced Strategy**: Good general performance, individual flexibility
- **Focus Fire Strategy**: Strong elimination potential, good coordination
- Both strategies handle edge cases and maintain safety protocols

The modular system makes it easy to experiment with different approaches and find the optimal strategy for various game scenarios.
