#!/usr/bin/env python3
"""
Test script to verify the splash bomb throwing logic works correctly.
"""

def manhattan_distance(x1, y1, x2, y2):
    return abs(x1 - x2) + abs(y1 - y2)

def get_splash_area(x, y, width, height):
    """Return list of all tiles affected by splash bomb (target + 8 surrounding tiles)."""
    splash_tiles = [(x, y)]  # Target tile
    # Add all 8 surrounding tiles (orthogonal + diagonal)
    directions = [(0, 1), (0, -1), (1, 0), (-1, 0), (1, 1), (1, -1), (-1, 1), (-1, -1)]
    for dx, dy in directions:
        nx, ny = x + dx, y + dy
        if 0 <= nx < width and 0 <= ny < height:
            splash_tiles.append((nx, ny))
    return splash_tiles

def is_safe_splash_target(target_x, target_y, my_agents, width, height):
    """Check if throwing a splash bomb at target won't hit any of our agents."""
    splash_area = get_splash_area(target_x, target_y, width, height)
    for agent in my_agents:
        if (agent['x'], agent['y']) in splash_area:
            return False
    return True

def find_best_splash_targets(agent_x, agent_y, enemy_agents, my_agents, width, height):
    """Find the best splash bomb targets that maximize enemy damage while avoiding friendly fire."""
    max_throw_range = 4
    best_targets = []

    # Consider all possible throw targets within range
    for target_x in range(max(0, agent_x - max_throw_range), min(width, agent_x + max_throw_range + 1)):
        for target_y in range(max(0, agent_y - max_throw_range), min(height, agent_y + max_throw_range + 1)):
            distance = manhattan_distance(agent_x, agent_y, target_x, target_y)
            if distance > max_throw_range:
                continue

            # Check if this target is safe (won't hit our agents)
            if not is_safe_splash_target(target_x, target_y, my_agents, width, height):
                continue

            # Count how many enemies would be hit
            splash_area = get_splash_area(target_x, target_y, width, height)
            enemies_hit = []
            total_enemy_wetness = 0
            for enemy in enemy_agents:
                if (enemy['x'], enemy['y']) in splash_area:
                    enemies_hit.append(enemy)
                    # Consider current wetness - prioritize enemies closer to elimination
                    total_enemy_wetness += enemy.get('wetness', 0)

            if enemies_hit:
                # Calculate total damage potential (30 wetness per enemy hit)
                total_damage = len(enemies_hit) * 30
                # Bonus for hitting enemies that are already damaged
                elimination_potential = sum(1 for enemy in enemies_hit if enemy.get('wetness', 0) + 30 >= 100)

                best_targets.append({
                    'target_pos': (target_x, target_y),
                    'distance': distance,
                    'enemies_hit': enemies_hit,
                    'total_damage': total_damage,
                    'enemy_count': len(enemies_hit),
                    'elimination_potential': elimination_potential,
                    'total_enemy_wetness': total_enemy_wetness
                })

    # Sort by: elimination potential, most enemies hit, total enemy wetness, then closest distance
    best_targets.sort(key=lambda x: (-x['elimination_potential'], -x['enemy_count'], -x['total_enemy_wetness'], x['distance']))
    return best_targets

def test_splash_area():
    """Test splash area calculation."""
    print("Testing splash area calculation...")
    
    # Test center of 5x5 grid
    splash = get_splash_area(2, 2, 5, 5)
    expected = [(2, 2), (2, 3), (2, 1), (3, 2), (1, 2), (3, 3), (3, 1), (1, 3), (1, 1)]
    assert set(splash) == set(expected), f"Expected {expected}, got {splash}"
    
    # Test corner case
    splash = get_splash_area(0, 0, 5, 5)
    expected = [(0, 0), (0, 1), (1, 0), (1, 1)]
    assert set(splash) == set(expected), f"Expected {expected}, got {splash}"
    
    print("✓ Splash area tests passed")

def test_safety_check():
    """Test friendly fire safety check."""
    print("Testing safety check...")
    
    my_agents = [
        {'x': 1, 'y': 1, 'agent_id': 1},
        {'x': 3, 'y': 3, 'agent_id': 2}
    ]
    
    # Safe target
    assert is_safe_splash_target(5, 5, my_agents, 10, 10) == True
    
    # Unsafe target (would hit agent at 1,1)
    assert is_safe_splash_target(2, 2, my_agents, 10, 10) == False
    
    # Unsafe target (would hit agent at 3,3)
    assert is_safe_splash_target(3, 4, my_agents, 10, 10) == False
    
    print("✓ Safety check tests passed")

def test_target_selection():
    """Test target selection logic."""
    print("Testing target selection...")
    
    # Setup test scenario
    agent_x, agent_y = 5, 5
    enemy_agents = [
        {'x': 3, 'y': 3, 'agent_id': 10, 'wetness': 0},
        {'x': 3, 'y': 4, 'agent_id': 11, 'wetness': 80},  # High wetness, good elimination target
        {'x': 7, 'y': 7, 'agent_id': 12, 'wetness': 0}
    ]
    my_agents = [
        {'x': 5, 'y': 5, 'agent_id': 1}
    ]
    
    targets = find_best_splash_targets(agent_x, agent_y, enemy_agents, my_agents, 10, 10)
    
    # Should find targets that can hit multiple enemies
    assert len(targets) > 0, "Should find at least one target"
    
    # Check that the best target prioritizes elimination potential
    best_target = targets[0]
    print(f"Best target: {best_target}")
    
    # Should prefer targets that can eliminate enemies (wetness + 30 >= 100)
    high_wetness_enemies = [e for e in best_target['enemies_hit'] if e['wetness'] >= 70]
    if high_wetness_enemies:
        assert best_target['elimination_potential'] > 0, "Should have elimination potential"
    
    print("✓ Target selection tests passed")

def test_range_limits():
    """Test throw range limits."""
    print("Testing range limits...")
    
    agent_x, agent_y = 5, 5
    enemy_agents = [
        {'x': 10, 'y': 10, 'agent_id': 10, 'wetness': 0}  # Too far (distance = 10)
    ]
    my_agents = [{'x': 5, 'y': 5, 'agent_id': 1}]
    
    targets = find_best_splash_targets(agent_x, agent_y, enemy_agents, my_agents, 15, 15)
    
    # Should not find targets beyond range 4
    assert len(targets) == 0, "Should not find targets beyond range 4"
    
    # Test enemy within range
    enemy_agents = [
        {'x': 7, 'y': 7, 'agent_id': 10, 'wetness': 0}  # Distance = 4, within range
    ]
    
    targets = find_best_splash_targets(agent_x, agent_y, enemy_agents, my_agents, 15, 15)
    assert len(targets) > 0, "Should find targets within range 4"
    
    print("✓ Range limit tests passed")

if __name__ == "__main__":
    print("Running splash bomb logic tests...\n")
    
    try:
        test_splash_area()
        test_safety_check()
        test_target_selection()
        test_range_limits()
        
        print("\n🎉 All tests passed! The splash bomb logic is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
